import { BaseAgent } from './base';
import { AgentCapability, AgentTaskResult, AgentStatus } from '../types';
import { factVerifierTool } from '../tools/fact-verifier';
import { generateText } from 'ai';
import { anthropic } from '@ai-sdk/anthropic';
import { DEEP_RESEARCH_SYSTEM_PROMPTS } from '../prompts/system-prompts';

interface FactCheckTask {
  type: 'verify-claim' | 'validate-sources' | 'check-report';
  content: string;
  sources?: any[];
  context?: string;
}

interface FactCheckResult {
  overallVerificationStatus: 'verified' | 'partially-verified' | 'unverified' | 'contains-false-claims';
  overallConfidence: number;
  claims: Array<{
    claim: string;
    verificationStatus: string;
    confidence: number;
    explanation: string;
    supportingSources: any[];
    issues?: string[];
  }>;
  sourceValidation: {
    totalSources: number;
    credibleSources: number;
    unreliableSources: number;
    averageCredibilityScore: number;
    sourceIssues: string[];
  };
  temporalIssues: string[];
  recommendations: string[];
  requiresRevision: boolean;
  revisionNotes?: string[];
}

export class FactCheckAgent extends BaseAgent {
  capabilities = [AgentCapability.FACT_CHECKING, AgentCapability.SOURCE_VALIDATION];
  
  async execute(task: string | FactCheckTask): Promise<AgentTaskResult> {
    console.log('[FactCheckAgent] Starting fact-check task');
    
    try {
      // Parse task if it's a string
      let factCheckTask: FactCheckTask;
      if (typeof task === 'string') {
        factCheckTask = {
          type: 'verify-claim',
          content: task,
          sources: []
        };
      } else {
        factCheckTask = task;
      }

      this.updateStatus(AgentStatus.WORKING);
      
      // Extract claims from content
      const claims = await this.extractClaims(factCheckTask.content);
      console.log(`[FactCheckAgent] Extracted ${claims.length} claims to verify`);
      
      // Validate sources first if provided
      let sourceValidation = null;
      if (factCheckTask.sources && factCheckTask.sources.length > 0) {
        sourceValidation = await this.validateSources(factCheckTask.sources);
        console.log('[FactCheckAgent] Source validation complete:', sourceValidation);
      }
      
      // Verify each claim
      const verifiedClaims: any[] = [];
      let totalConfidence = 0;
      let hasUnverifiedClaims = false;
      let hasFalseClaims = false;
      
      for (const claim of claims) {
        const verificationResult = await factVerifierTool.execute({
          claim: claim.text,
          sources: factCheckTask.sources || [],
          context: factCheckTask.context,
          currentDate: new Date().toISOString()
        });
        
        if (verificationResult.success && verificationResult.data) {
          const data = verificationResult.data;
          verifiedClaims.push({
            claim: claim.text,
            verificationStatus: data.verificationStatus,
            confidence: data.confidence,
            explanation: data.explanation,
            supportingSources: data.supportingSources,
            issues: data.temporalAccuracy?.issues || []
          });
          
          totalConfidence += data.confidence;
          if (data.verificationStatus === 'unverified') hasUnverifiedClaims = true;
          if (data.verificationStatus === 'false') hasFalseClaims = true;
        } else {
          // Failed to verify claim
          verifiedClaims.push({
            claim: claim.text,
            verificationStatus: 'unverified',
            confidence: 0,
            explanation: 'Verification failed',
            supportingSources: [],
            issues: ['Verification process failed']
          });
          hasUnverifiedClaims = true;
        }
      }
      
      // Calculate overall status
      const averageConfidence = claims.length > 0 ? totalConfidence / claims.length : 0;
      let overallStatus: FactCheckResult['overallVerificationStatus'];
      
      if (hasFalseClaims) {
        overallStatus = 'contains-false-claims';
      } else if (hasUnverifiedClaims) {
        overallStatus = 'unverified';
      } else if (averageConfidence >= 80) {
        overallStatus = 'verified';
      } else {
        overallStatus = 'partially-verified';
      }
      
      // Extract temporal issues
      const temporalIssues = verifiedClaims
        .flatMap(vc => vc.issues || [])
        .filter((issue, index, self) => self.indexOf(issue) === index);
      
      // Generate recommendations
      const recommendations = await this.generateRecommendations(
        verifiedClaims,
        sourceValidation,
        temporalIssues
      );
      
      // Determine if revision is needed
      const requiresRevision = overallStatus === 'contains-false-claims' || 
                             overallStatus === 'unverified' ||
                             averageConfidence < 60 ||
                             temporalIssues.length > 0;
      
      const result: FactCheckResult = {
        overallVerificationStatus: overallStatus,
        overallConfidence: averageConfidence,
        claims: verifiedClaims,
        sourceValidation: sourceValidation || {
          totalSources: 0,
          credibleSources: 0,
          unreliableSources: 0,
          averageCredibilityScore: 0,
          sourceIssues: []
        },
        temporalIssues,
        recommendations,
        requiresRevision,
        revisionNotes: requiresRevision ? this.generateRevisionNotes(verifiedClaims, temporalIssues) : undefined
      };
      
      this.updateStatus(AgentStatus.COMPLETED);
      
      console.log('[FactCheckAgent] Fact-check complete:', {
        status: result.overallVerificationStatus,
        confidence: result.overallConfidence,
        requiresRevision: result.requiresRevision
      });
      
      return {
        success: true,
        data: result,
        confidence: averageConfidence / 100 // Convert to 0-1 scale
      };
      
    } catch (error) {
      console.error('[FactCheckAgent] Error during fact-checking:', error);
      this.updateStatus(AgentStatus.FAILED);
      
      return {
        success: false,
        data: { 
          error: error instanceof Error ? error.message : 'Unknown error',
          task: typeof task === 'string' ? task : task.content
        },
        confidence: 0
      };
    }
  }
  
  private async extractClaims(content: string): Promise<Array<{ text: string; type: string }>> {
    try {
      const result = await generateText({
        model: anthropic('claude-3-5-sonnet-20241022'),
        messages: [
          {
            role: 'system',
            content: 'Extract all factual claims from the provided content. Return a JSON array of objects with "text" (the claim) and "type" (fact/opinion/speculation) fields.'
          },
          {
            role: 'user',
            content: `Extract all claims from this content:\n\n${content}\n\nReturn only a JSON array.`
          }
        ],
        temperature: 0.3,
        maxOutputTokens: 2000
      });
      
      const jsonMatch = result.text.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Fallback: treat entire content as one claim
      return [{ text: content, type: 'fact' }];
      
    } catch (error) {
      console.error('[FactCheckAgent] Error extracting claims:', error);
      // Fallback: treat entire content as one claim
      return [{ text: content, type: 'fact' }];
    }
  }
  
  private async validateSources(sources: any[]): Promise<FactCheckResult['sourceValidation']> {
    try {
      const result = await generateText({
        model: anthropic('claude-3-5-sonnet-20241022'),
        messages: [
          {
            role: 'system',
            content: DEEP_RESEARCH_SYSTEM_PROMPTS.SOURCE_VALIDATOR
          },
          {
            role: 'user',
            content: `Validate these sources and provide a summary:\n\n${JSON.stringify(sources, null, 2)}\n\nReturn a JSON object with totalSources, credibleSources, unreliableSources, averageCredibilityScore, and sourceIssues array.`
          }
        ],
        temperature: 0.3,
        maxOutputTokens: 1500
      });
      
      const jsonMatch = result.text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Fallback
      return {
        totalSources: sources.length,
        credibleSources: 0,
        unreliableSources: 0,
        averageCredibilityScore: 5,
        sourceIssues: ['Unable to validate sources']
      };
      
    } catch (error) {
      console.error('[FactCheckAgent] Error validating sources:', error);
      return {
        totalSources: sources.length,
        credibleSources: 0,
        unreliableSources: 0,
        averageCredibilityScore: 0,
        sourceIssues: ['Source validation failed']
      };
    }
  }
  
  private async generateRecommendations(
    verifiedClaims: any[],
    sourceValidation: any,
    temporalIssues: string[]
  ): Promise<string[]> {
    const recommendations: string[] = [];
    
    // Check for unverified claims
    const unverifiedClaims = verifiedClaims.filter(vc => vc.verificationStatus === 'unverified');
    if (unverifiedClaims.length > 0) {
      recommendations.push(`Find additional sources to verify ${unverifiedClaims.length} unverified claims`);
    }
    
    // Check for false claims
    const falseClaims = verifiedClaims.filter(vc => vc.verificationStatus === 'false');
    if (falseClaims.length > 0) {
      recommendations.push(`Remove or correct ${falseClaims.length} false claims`);
    }
    
    // Check for low confidence claims
    const lowConfidenceClaims = verifiedClaims.filter(vc => vc.confidence < 60);
    if (lowConfidenceClaims.length > 0) {
      recommendations.push(`Strengthen evidence for ${lowConfidenceClaims.length} low-confidence claims`);
    }
    
    // Source quality issues
    if (sourceValidation && sourceValidation.averageCredibilityScore < 7) {
      recommendations.push('Include more authoritative sources to improve credibility');
    }
    
    // Temporal issues
    if (temporalIssues.length > 0) {
      recommendations.push('Update temporal references to ensure accuracy');
    }
    
    // Citation gaps
    const claimsWithoutSources = verifiedClaims.filter(vc => 
      (!vc.supportingSources || vc.supportingSources.length === 0) && 
      vc.verificationStatus !== 'false'
    );
    if (claimsWithoutSources.length > 0) {
      recommendations.push(`Add citations for ${claimsWithoutSources.length} unsourced claims`);
    }
    
    return recommendations;
  }
  
  private generateRevisionNotes(verifiedClaims: any[], temporalIssues: string[]): string[] {
    const notes: string[] = [];
    
    // False claims that need removal/correction
    verifiedClaims
      .filter(vc => vc.verificationStatus === 'false')
      .forEach(vc => {
        notes.push(`REMOVE/CORRECT: "${vc.claim}" - ${vc.explanation}`);
      });
    
    // Unverified claims that need sources
    verifiedClaims
      .filter(vc => vc.verificationStatus === 'unverified')
      .forEach(vc => {
        notes.push(`NEEDS SOURCE: "${vc.claim}" - Currently unverifiable`);
      });
    
    // Low confidence claims that need strengthening
    verifiedClaims
      .filter(vc => vc.confidence < 60 && vc.verificationStatus !== 'false')
      .forEach(vc => {
        notes.push(`STRENGTHEN: "${vc.claim}" - Confidence only ${vc.confidence}%`);
      });
    
    // Temporal issues
    temporalIssues.forEach(issue => {
      notes.push(`TEMPORAL FIX: ${issue}`);
    });
    
    return notes;
  }
}