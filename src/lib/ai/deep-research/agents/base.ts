/**
 * Base Agent - Common functionality for all research agents
 */

import type { AgentCapability, AgentTaskResult } from '../types';
import { AgentStatus } from '../types';
import { EventEmitter } from 'events';

export abstract class BaseAgent extends EventEmitter {
  protected status: AgentStatus = AgentStatus.IDLE;
  protected startTime?: Date;
  protected endTime?: Date;
  
  constructor(
    public id: string,
    public name: string,
    public model: any
  ) {
    super();
  }
  
  abstract capabilities: AgentCapability[];
  
  abstract execute(task: string | any): Promise<AgentTaskResult>;
  
  /**
   * Update agent status and emit status change event
   */
  protected updateStatus(status: AgentStatus): void {
    const previousStatus = this.status;
    this.status = status;
    
    if (status === AgentStatus.WORKING && !this.startTime) {
      this.startTime = new Date();
    } else if ([AgentStatus.COMPLETED, AgentStatus.FAILED].includes(status) && !this.endTime) {
      this.endTime = new Date();
    }
    
    this.emit('status-change', {
      agentId: this.id,
      previousStatus,
      currentStatus: status,
      timestamp: new Date()
    });
  }
  
  /**
   * Get current agent status
   */
  getStatus(): AgentStatus {
    return this.status;
  }
  
  /**
   * Get execution time in milliseconds
   */
  getExecutionTime(): number | null {
    if (this.startTime && this.endTime) {
      return this.endTime.getTime() - this.startTime.getTime();
    }
    return null;
  }
  
  /**
   * Reset agent state
   */
  reset(): void {
    this.status = AgentStatus.IDLE;
    this.startTime = undefined;
    this.endTime = undefined;
  }
}