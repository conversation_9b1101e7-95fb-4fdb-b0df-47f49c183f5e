import { Tool, ToolResult } from '../types';
import { z } from 'zod';

interface OfficialSourceParams {
  query: string;
  source: 'data.gov' | 'world-bank' | 'who' | 'un' | 'all';
  dataType?: 'statistics' | 'reports' | 'datasets' | 'all';
  country?: string;
  startYear?: number;
  endYear?: number;
}

interface OfficialDataResult {
  id: string;
  title: string;
  description: string;
  source: string;
  publishedDate: string;
  url: string;
  dataType: string;
  format?: string;
  organization: string;
  country?: string;
  metadata?: Record<string, any>;
}

/**
 * Search US Government open data portal
 */
async function searchDataGov(query: string, limit: number = 10): Promise<OfficialDataResult[]> {
  try {
    const baseUrl = 'https://catalog.data.gov/api/3/action/package_search';
    const params = new URLSearchParams({
      q: query,
      rows: limit.toString(),
      sort: 'score desc'
    });

    const response = await fetch(`${baseUrl}?${params}`);
    if (!response.ok) {
      throw new Error(`data.gov API error: ${response.status}`);
    }

    const data = await response.json();
    const results: OfficialDataResult[] = [];

    for (const dataset of data.result?.results || []) {
      results.push({
        id: dataset.id,
        title: dataset.title || '',
        description: dataset.notes || '',
        source: 'data.gov',
        publishedDate: dataset.metadata_created || '',
        url: `https://catalog.data.gov/dataset/${dataset.name}`,
        dataType: 'dataset',
        format: dataset.resources?.[0]?.format || 'various',
        organization: dataset.organization?.title || 'US Government',
        country: 'United States',
        metadata: {
          tags: dataset.tags?.map((t: any) => t.name) || [],
          resources: dataset.num_resources || 0
        }
      });
    }

    return results;
  } catch (error) {
    console.error('[OfficialSources] data.gov search failed:', error);
    return [];
  }
}

/**
 * Search World Bank open data
 */
async function searchWorldBank(query: string, limit: number = 10): Promise<OfficialDataResult[]> {
  try {
    // World Bank API v2
    const baseUrl = 'https://api.worldbank.org/v2/sources';
    const response = await fetch(`${baseUrl}?format=json&per_page=${limit}`);
    
    if (!response.ok) {
      throw new Error(`World Bank API error: ${response.status}`);
    }

    const [metadata, sources] = await response.json();
    const results: OfficialDataResult[] = [];

    // Search through available data sources
    for (const source of sources || []) {
      if (source.name?.toLowerCase().includes(query.toLowerCase()) ||
          source.description?.toLowerCase().includes(query.toLowerCase())) {
        results.push({
          id: source.id,
          title: source.name || '',
          description: source.description || '',
          source: 'World Bank',
          publishedDate: source.lastupdated || '',
          url: source.url || `https://data.worldbank.org/`,
          dataType: 'statistics',
          organization: 'World Bank Group',
          metadata: {
            code: source.code,
            concepts: source.concepts || 0
          }
        });
      }
    }

    return results;
  } catch (error) {
    console.error('[OfficialSources] World Bank search failed:', error);
    return [];
  }
}

/**
 * Search WHO (World Health Organization) data
 */
async function searchWHO(query: string, limit: number = 10): Promise<OfficialDataResult[]> {
  try {
    // WHO doesn't have a direct search API, so we'll provide curated links
    const whoDatasets = [
      {
        id: 'who-covid-dashboard',
        title: 'WHO COVID-19 Dashboard',
        description: 'Global COVID-19 data and statistics',
        url: 'https://covid19.who.int/',
        topics: ['covid', 'pandemic', 'coronavirus', 'health']
      },
      {
        id: 'who-global-health-observatory',
        title: 'Global Health Observatory',
        description: 'Data repository on global health statistics',
        url: 'https://www.who.int/data/gho',
        topics: ['health', 'statistics', 'disease', 'mortality']
      },
      {
        id: 'who-health-emergency-dashboard',
        title: 'Health Emergency Dashboard',
        description: 'WHO Health Emergencies Programme data',
        url: 'https://extranet.who.int/publicemergency',
        topics: ['emergency', 'outbreak', 'crisis', 'epidemic']
      }
    ];

    const results: OfficialDataResult[] = [];
    const searchTerms = query.toLowerCase().split(' ');

    for (const dataset of whoDatasets) {
      const relevance = searchTerms.some(term => 
        dataset.topics.some(topic => topic.includes(term)) ||
        dataset.title.toLowerCase().includes(term) ||
        dataset.description.toLowerCase().includes(term)
      );

      if (relevance) {
        results.push({
          id: dataset.id,
          title: dataset.title,
          description: dataset.description,
          source: 'WHO',
          publishedDate: new Date().toISOString(),
          url: dataset.url,
          dataType: 'statistics',
          organization: 'World Health Organization'
        });
      }
    }

    return results.slice(0, limit);
  } catch (error) {
    console.error('[OfficialSources] WHO search failed:', error);
    return [];
  }
}

/**
 * Search UN data portal
 */
async function searchUN(query: string, limit: number = 10): Promise<OfficialDataResult[]> {
  try {
    // UN Data doesn't have a public API, provide curated resources
    const unResources = [
      {
        id: 'undata-portal',
        title: 'UNdata Portal',
        description: 'Statistical database of the United Nations',
        url: 'http://data.un.org/',
        topics: ['statistics', 'development', 'population', 'economy']
      },
      {
        id: 'un-sdg-indicators',
        title: 'Sustainable Development Goals Indicators',
        description: 'Global database for SDG indicators',
        url: 'https://unstats.un.org/sdgs/dataportal',
        topics: ['sdg', 'sustainable', 'development', 'goals']
      },
      {
        id: 'unhcr-refugee-data',
        title: 'UNHCR Refugee Data',
        description: 'Global trends and statistics on refugees',
        url: 'https://www.unhcr.org/refugee-statistics/',
        topics: ['refugee', 'migration', 'displacement', 'asylum']
      }
    ];

    const results: OfficialDataResult[] = [];
    const searchTerms = query.toLowerCase().split(' ');

    for (const resource of unResources) {
      const relevance = searchTerms.some(term => 
        resource.topics.some(topic => topic.includes(term)) ||
        resource.title.toLowerCase().includes(term) ||
        resource.description.toLowerCase().includes(term)
      );

      if (relevance) {
        results.push({
          id: resource.id,
          title: resource.title,
          description: resource.description,
          source: 'United Nations',
          publishedDate: new Date().toISOString(),
          url: resource.url,
          dataType: 'statistics',
          organization: 'United Nations'
        });
      }
    }

    return results.slice(0, limit);
  } catch (error) {
    console.error('[OfficialSources] UN search failed:', error);
    return [];
  }
}

export const officialSourcesTool: Tool = {
  id: 'officialSources',
  name: 'Official Government & Organization Data',
  description: 'Search official government and international organization data sources',
  execute: async (params: OfficialSourceParams): Promise<ToolResult> => {
    const {
      query,
      source = 'all',
      dataType = 'all',
      country,
      startYear,
      endYear
    } = params;

    console.log(`[OfficialSources] Searching for: ${query} in ${source}`);

    try {
      let allResults: OfficialDataResult[] = [];

      // Search selected sources
      if (source === 'all' || source === 'data.gov') {
        const dataGovResults = await searchDataGov(query, 10);
        allResults = allResults.concat(dataGovResults);
      }

      if (source === 'all' || source === 'world-bank') {
        const worldBankResults = await searchWorldBank(query, 10);
        allResults = allResults.concat(worldBankResults);
      }

      if (source === 'all' || source === 'who') {
        const whoResults = await searchWHO(query, 10);
        allResults = allResults.concat(whoResults);
      }

      if (source === 'all' || source === 'un') {
        const unResults = await searchUN(query, 10);
        allResults = allResults.concat(unResults);
      }

      // Filter by data type
      if (dataType !== 'all') {
        allResults = allResults.filter(result => 
          result.dataType === dataType
        );
      }

      // Filter by country
      if (country) {
        allResults = allResults.filter(result => 
          result.country?.toLowerCase().includes(country.toLowerCase()) ||
          result.title.toLowerCase().includes(country.toLowerCase()) ||
          result.description.toLowerCase().includes(country.toLowerCase())
        );
      }

      // Filter by year range
      if (startYear || endYear) {
        allResults = allResults.filter(result => {
          const year = new Date(result.publishedDate).getFullYear();
          const afterStart = !startYear || year >= startYear;
          const beforeEnd = !endYear || year <= endYear;
          return afterStart && beforeEnd;
        });
      }

      console.log(`[OfficialSources] Found ${allResults.length} results`);

      // Provide additional resources
      const additionalResources = [
        {
          name: 'OECD Data',
          url: 'https://data.oecd.org/',
          description: 'Economic and social data from OECD countries'
        },
        {
          name: 'Eurostat',
          url: 'https://ec.europa.eu/eurostat',
          description: 'European Union statistical office'
        },
        {
          name: 'IMF Data',
          url: 'https://www.imf.org/en/Data',
          description: 'International Monetary Fund economic data'
        },
        {
          name: 'World Health Statistics',
          url: 'https://www.who.int/data/gho/publications/world-health-statistics',
          description: 'Annual WHO compilation of health statistics'
        }
      ];

      return {
        success: true,
        data: {
          results: allResults,
          totalFound: allResults.length,
          sources: {
            dataGov: allResults.filter(r => r.source === 'data.gov').length,
            worldBank: allResults.filter(r => r.source === 'World Bank').length,
            who: allResults.filter(r => r.source === 'WHO').length,
            un: allResults.filter(r => r.source === 'United Nations').length
          },
          additionalResources,
          searchCriteria: {
            query,
            source,
            dataType,
            country,
            yearRange: startYear || endYear ? { start: startYear, end: endYear } : null
          }
        },
        metadata: {
          tool: 'officialSources',
          timestamp: new Date().toISOString(),
          sourcesSearched: source === 'all' ? ['data.gov', 'world-bank', 'who', 'un'] : [source]
        }
      };

    } catch (error) {
      console.error('[OfficialSources] Search failed:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Official sources search failed',
        data: {
          results: [],
          totalFound: 0,
          sources: { dataGov: 0, worldBank: 0, who: 0, un: 0 },
          additionalResources: [],
          searchCriteria: { query, source, dataType, country }
        },
        metadata: {
          tool: 'officialSources',
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }
};