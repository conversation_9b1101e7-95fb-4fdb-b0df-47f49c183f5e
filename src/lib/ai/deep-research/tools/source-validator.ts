import { Tool, ToolResult } from '../types';
import { generateText } from 'ai';
import { anthropic } from '@ai-sdk/anthropic';
import { DEEP_RESEARCH_SYSTEM_PROMPTS } from '../prompts/system-prompts';

interface SourceValidationParams {
  sources: Array<{
    url: string;
    title: string;
    content?: string;
    publishedDate?: string;
    author?: string;
    domain?: string;
  }>;
  topic?: string;
  requirePeerReview?: boolean;
  minCredibilityScore?: number;
}

interface SourceValidationResult {
  source: {
    url: string;
    title: string;
    domain?: string;
  };
  credibilityScore: number; // 0-10
  category: 'primary' | 'secondary' | 'tertiary' | 'unreliable';
  credibilityFactors: {
    authority: number;
    accuracy: number;
    objectivity: number;
    currency: number;
    coverage: number;
  };
  issues: string[];
  strengths: string[];
  biases: string[];
  recommendation: 'use' | 'use-with-caution' | 'do-not-use';
  rationale: string;
}

interface ValidationSummary {
  totalSources: number;
  validatedSources: SourceValidationResult[];
  overallCredibility: number;
  sourceCategoryCounts: {
    primary: number;
    secondary: number;
    tertiary: number;
    unreliable: number;
  };
  recommendations: string[];
  redFlags: string[];
  academicSourceCount: number;
  officialSourceCount: number;
}

export const sourceValidatorTool: Tool = {
  id: 'sourceValidator',
  name: 'Source Validator',
  description: 'Validate and assess credibility of information sources',
  execute: async (params: SourceValidationParams): Promise<ToolResult> => {
    const { 
      sources = [], 
      topic = 'general research',
      requirePeerReview = false,
      minCredibilityScore = 5
    } = params;
    
    console.log(`[SourceValidator] Validating ${sources.length} sources`);
    
    try {
      const validatedSources: SourceValidationResult[] = [];
      let totalCredibility = 0;
      const categoryCounts = {
        primary: 0,
        secondary: 0,
        tertiary: 0,
        unreliable: 0
      };
      
      // Validate each source
      for (const source of sources) {
        const validationResult = await validateSingleSource(source, topic);
        validatedSources.push(validationResult);
        
        totalCredibility += validationResult.credibilityScore;
        categoryCounts[validationResult.category]++;
      }
      
      // Calculate overall metrics
      const overallCredibility = sources.length > 0 ? totalCredibility / sources.length : 0;
      
      // Count academic and official sources
      const academicDomains = ['.edu', '.ac.uk', 'scholar.', 'arxiv.org', 'pubmed', 'jstor'];
      const officialDomains = ['.gov', '.mil', 'un.org', 'who.int', 'europa.eu'];
      
      const academicSourceCount = validatedSources.filter(vs => 
        academicDomains.some(domain => vs.source.url.includes(domain))
      ).length;
      
      const officialSourceCount = validatedSources.filter(vs => 
        officialDomains.some(domain => vs.source.url.includes(domain))
      ).length;
      
      // Generate recommendations
      const recommendations: string[] = [];
      const redFlags: string[] = [];
      
      if (overallCredibility < 6) {
        recommendations.push('Consider finding more authoritative sources to improve research credibility');
      }
      
      if (categoryCounts.unreliable > 0) {
        redFlags.push(`${categoryCounts.unreliable} unreliable sources detected - recommend removal or replacement`);
      }
      
      if (categoryCounts.primary === 0) {
        recommendations.push('Add primary sources (original research, official data) for stronger evidence');
      }
      
      if (requirePeerReview && academicSourceCount === 0) {
        redFlags.push('No peer-reviewed academic sources found despite requirement');
      }
      
      if (validatedSources.filter(vs => vs.credibilityScore < minCredibilityScore).length > 0) {
        const lowCredCount = validatedSources.filter(vs => vs.credibilityScore < minCredibilityScore).length;
        redFlags.push(`${lowCredCount} sources below minimum credibility score of ${minCredibilityScore}`);
      }
      
      // Check for bias patterns
      const biasedSources = validatedSources.filter(vs => vs.biases.length > 0);
      if (biasedSources.length > sources.length / 2) {
        redFlags.push('Majority of sources show potential bias - seek more balanced perspectives');
      }
      
      // Check for outdated sources
      const outdatedSources = validatedSources.filter(vs => 
        vs.issues.some(issue => issue.toLowerCase().includes('outdated') || issue.toLowerCase().includes('old'))
      );
      if (outdatedSources.length > 0) {
        recommendations.push(`Update ${outdatedSources.length} potentially outdated sources with more recent information`);
      }
      
      const summary: ValidationSummary = {
        totalSources: sources.length,
        validatedSources,
        overallCredibility,
        sourceCategoryCounts: categoryCounts,
        recommendations,
        redFlags,
        academicSourceCount,
        officialSourceCount
      };
      
      console.log(`[SourceValidator] Validation complete:`, {
        overallCredibility: overallCredibility.toFixed(1),
        categories: categoryCounts,
        redFlags: redFlags.length
      });
      
      return {
        success: true,
        data: summary,
        metadata: {
          tool: 'sourceValidator',
          timestamp: new Date().toISOString(),
          topic,
          requirePeerReview,
          minCredibilityScore
        }
      };
      
    } catch (error) {
      console.error('[SourceValidator] Error during validation:', error);
      
      return {
        success: false,
        data: {
          totalSources: sources.length,
          validatedSources: [],
          overallCredibility: 0,
          sourceCategoryCounts: {
            primary: 0,
            secondary: 0,
            tertiary: 0,
            unreliable: 0
          },
          recommendations: ['Source validation failed - manual review required'],
          redFlags: ['Unable to validate sources automatically'],
          academicSourceCount: 0,
          officialSourceCount: 0
        },
        error: error instanceof Error ? error.message : 'Source validation failed',
        metadata: {
          tool: 'sourceValidator',
          timestamp: new Date().toISOString()
        }
      };
    }
  }
};

async function validateSingleSource(
  source: SourceValidationParams['sources'][0],
  topic: string
): Promise<SourceValidationResult> {
  try {
    const result = await generateText({
      model: anthropic('claude-3-5-sonnet-20241022'),
      messages: [
        {
          role: 'system',
          content: DEEP_RESEARCH_SYSTEM_PROMPTS.SOURCE_VALIDATOR
        },
        {
          role: 'user',
          content: `
Validate this source for research on "${topic}":

URL: ${source.url}
Title: ${source.title}
Domain: ${source.domain || new URL(source.url).hostname}
Published: ${source.publishedDate || 'Unknown'}
Author: ${source.author || 'Unknown'}
${source.content ? `Content excerpt: ${source.content.substring(0, 500)}...` : ''}

Provide a JSON response with:
{
  "credibilityScore": 0-10,
  "category": "primary" | "secondary" | "tertiary" | "unreliable",
  "credibilityFactors": {
    "authority": 0-10,
    "accuracy": 0-10,
    "objectivity": 0-10,
    "currency": 0-10,
    "coverage": 0-10
  },
  "issues": ["..."],
  "strengths": ["..."],
  "biases": ["..."],
  "recommendation": "use" | "use-with-caution" | "do-not-use",
  "rationale": "..."
}`
        }
      ],
      temperature: 0.3,
      maxOutputTokens: 1500
    });
    
    // Parse response
    const jsonMatch = result.text.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const validationData = JSON.parse(jsonMatch[0]);
      
      return {
        source: {
          url: source.url,
          title: source.title,
          domain: source.domain || new URL(source.url).hostname
        },
        credibilityScore: validationData.credibilityScore || 5,
        category: validationData.category || 'secondary',
        credibilityFactors: validationData.credibilityFactors || {
          authority: 5,
          accuracy: 5,
          objectivity: 5,
          currency: 5,
          coverage: 5
        },
        issues: validationData.issues || [],
        strengths: validationData.strengths || [],
        biases: validationData.biases || [],
        recommendation: validationData.recommendation || 'use-with-caution',
        rationale: validationData.rationale || 'Unable to fully assess source'
      };
    }
    
    // Fallback
    return createFallbackValidation(source);
    
  } catch (error) {
    console.error('[SourceValidator] Error validating source:', source.url, error);
    return createFallbackValidation(source);
  }
}

function createFallbackValidation(source: SourceValidationParams['sources'][0]): SourceValidationResult {
  // Basic heuristic validation
  const url = source.url.toLowerCase();
  const domain = new URL(source.url).hostname;
  
  let credibilityScore = 5;
  let category: SourceValidationResult['category'] = 'secondary';
  let recommendation: SourceValidationResult['recommendation'] = 'use-with-caution';
  
  // Boost for known authoritative domains
  if (url.includes('.gov') || url.includes('.edu') || url.includes('.mil')) {
    credibilityScore = 8;
    category = 'primary';
    recommendation = 'use';
  } else if (url.includes('wikipedia.org') || url.includes('wikimedia.org')) {
    credibilityScore = 6;
    category = 'tertiary';
  } else if (url.includes('blog') || url.includes('medium.com') || url.includes('substack')) {
    credibilityScore = 4;
    category = 'secondary';
  }
  
  return {
    source: {
      url: source.url,
      title: source.title,
      domain
    },
    credibilityScore,
    category,
    credibilityFactors: {
      authority: credibilityScore,
      accuracy: credibilityScore,
      objectivity: credibilityScore,
      currency: source.publishedDate ? 7 : 5,
      coverage: 5
    },
    issues: ['Automated validation only - manual review recommended'],
    strengths: [],
    biases: [],
    recommendation,
    rationale: 'Basic heuristic validation applied'
  };
}