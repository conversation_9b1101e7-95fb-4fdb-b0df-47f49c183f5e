import { Tool, ToolResult } from '../types';
import { generateText } from 'ai';
import { anthropic } from '@ai-sdk/anthropic';
import { DEEP_RESEARCH_SYSTEM_PROMPTS } from '../prompts/system-prompts';

interface FactVerificationParams {
  claim: string;
  sources: Array<{
    url: string;
    title: string;
    content?: string;
    publishedDate?: string;
    credibilityScore?: number;
  }>;
  context?: string;
  currentDate?: string;
}

interface VerificationResult {
  claim: string;
  verified: boolean;
  verificationStatus: 'verified' | 'partially-verified' | 'unverified' | 'false';
  confidence: number; // 0-100
  explanation: string;
  supportingSources: any[];
  contradictingSources: any[];
  temporalAccuracy: {
    valid: boolean;
    issues?: string[];
  };
  factualAnalysis: {
    claimType: 'fact' | 'opinion' | 'speculation' | 'mixed';
    requiresCitation: boolean;
    hasCitation: boolean;
    citationQuality?: 'high' | 'medium' | 'low';
  };
}

export const factVerifierTool: Tool = {
  id: 'factVerifier',
  name: 'Fact Verifier',
  description: 'Verify facts and claims by cross-referencing multiple sources and checking temporal accuracy',
  execute: async (params: FactVerificationParams): Promise<ToolResult> => {
    const { claim, sources = [], context = '', currentDate = new Date().toISOString() } = params;
    
    console.log(`[FactVerifier] Verifying claim: ${claim}`);
    console.log(`[FactVerifier] Using ${sources.length} sources for verification`);
    
    try {
      // Use AI to analyze the claim against sources
      const verificationResult = await generateText({
        model: anthropic('claude-3-5-sonnet-20241022'),
        messages: [
          {
            role: 'system',
            content: DEEP_RESEARCH_SYSTEM_PROMPTS.FACT_CHECKER
          },
          {
            role: 'user',
            content: `
Current Date: ${currentDate}
Context: ${context}

Claim to verify: "${claim}"

Sources provided:
${sources.map((s, i) => `
Source ${i + 1}:
- URL: ${s.url}
- Title: ${s.title}
- Published: ${s.publishedDate || 'Unknown'}
- Credibility Score: ${s.credibilityScore || 'Not assessed'}/10
- Content excerpt: ${s.content ? s.content.substring(0, 500) + '...' : 'No content provided'}
`).join('\n')}

Please analyze this claim and provide a detailed fact-check following the FACT_CHECKER protocol. Return a JSON response with:
{
  "verificationStatus": "verified" | "partially-verified" | "unverified" | "false",
  "confidence": 0-100,
  "explanation": "detailed explanation",
  "supportingSources": [{"sourceIndex": number, "relevantQuote": "...", "strength": "strong"|"moderate"|"weak"}],
  "contradictingSources": [{"sourceIndex": number, "relevantQuote": "...", "issue": "..."}],
  "temporalAccuracy": {"valid": boolean, "issues": ["..."]},
  "factualAnalysis": {
    "claimType": "fact"|"opinion"|"speculation"|"mixed",
    "requiresCitation": boolean,
    "hasCitation": boolean,
    "citationQuality": "high"|"medium"|"low"|null
  },
  "redFlags": ["..."],
  "recommendations": ["..."]
}`
          }
        ],
        temperature: 0.3,
        maxOutputTokens: 2000
      });

      // Parse the AI response
      let verificationData: any;
      try {
        // Extract JSON from the response
        const jsonMatch = verificationResult.text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          verificationData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in response');
        }
      } catch (parseError) {
        console.error('[FactVerifier] Failed to parse AI response:', parseError);
        // Fallback to basic verification
        verificationData = {
          verificationStatus: 'unverified',
          confidence: 0,
          explanation: 'Unable to fully verify claim due to processing error',
          supportingSources: [],
          contradictingSources: [],
          temporalAccuracy: { valid: true },
          factualAnalysis: {
            claimType: 'fact',
            requiresCitation: true,
            hasCitation: sources.length > 0
          }
        };
      }

      // Map supporting sources with full source data
      const supportingSourcesWithData = verificationData.supportingSources?.map((s: any) => ({
        ...s,
        source: sources[s.sourceIndex] || null
      })) || [];

      const contradictingSourcesWithData = verificationData.contradictingSources?.map((s: any) => ({
        ...s,
        source: sources[s.sourceIndex] || null
      })) || [];

      const result: VerificationResult = {
        claim,
        verified: verificationData.verificationStatus === 'verified',
        verificationStatus: verificationData.verificationStatus || 'unverified',
        confidence: verificationData.confidence || 0,
        explanation: verificationData.explanation || 'Verification incomplete',
        supportingSources: supportingSourcesWithData,
        contradictingSources: contradictingSourcesWithData,
        temporalAccuracy: verificationData.temporalAccuracy || { valid: true },
        factualAnalysis: verificationData.factualAnalysis || {
          claimType: 'fact',
          requiresCitation: true,
          hasCitation: false
        }
      };

      // Additional temporal validation
      if (claim.toLowerCase().includes('latest') || claim.toLowerCase().includes('current')) {
        const hasRecentSources = sources.some(s => {
          if (s.publishedDate) {
            const sourceDate = new Date(s.publishedDate);
            const daysSincePublished = (new Date(currentDate).getTime() - sourceDate.getTime()) / (1000 * 60 * 60 * 24);
            return daysSincePublished < 90; // Consider sources from last 90 days as recent
          }
          return false;
        });

        if (!hasRecentSources) {
          result.temporalAccuracy.valid = false;
          result.temporalAccuracy.issues = result.temporalAccuracy.issues || [];
          result.temporalAccuracy.issues.push('No recent sources found for claim about "latest" or "current" information');
          result.confidence = Math.min(result.confidence, 50);
        }
      }

      // Log verification results
      console.log(`[FactVerifier] Verification complete:`, {
        status: result.verificationStatus,
        confidence: result.confidence,
        supportingSources: result.supportingSources.length,
        contradictingSources: result.contradictingSources.length
      });

      return {
        success: true,
        data: result,
        metadata: {
          tool: 'factVerifier',
          timestamp: new Date().toISOString(),
          sourcesAnalyzed: sources.length,
          verificationStatus: result.verificationStatus,
          confidence: result.confidence,
          redFlags: verificationData.redFlags || [],
          recommendations: verificationData.recommendations || []
        },
      };
    } catch (error) {
      console.error('[FactVerifier] Error during fact verification:', error);
      
      return {
        success: false,
        data: {
          claim,
          verified: false,
          verificationStatus: 'unverified',
          confidence: 0,
          explanation: `Fact verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          supportingSources: [],
          contradictingSources: [],
          temporalAccuracy: { valid: false, issues: ['Verification failed'] },
          factualAnalysis: {
            claimType: 'fact',
            requiresCitation: true,
            hasCitation: false
          }
        },
        error: error instanceof Error ? error.message : 'Fact verification failed',
        metadata: {
          tool: 'factVerifier',
          timestamp: new Date().toISOString(),
          sourcesAnalyzed: sources.length
        },
      };
    }
  },
};