import { Tool, ToolResult } from '../types';
import { z } from 'zod';

interface AcademicSearchParams {
  query: string;
  source: 'arxiv' | 'pubmed' | 'doaj' | 'all';
  maxResults?: number;
  startDate?: string;
  endDate?: string;
  category?: string;
}

interface AcademicPaper {
  id: string;
  title: string;
  authors: string[];
  abstract: string;
  publishedDate: string;
  source: string;
  url: string;
  doi?: string;
  categories?: string[];
  pdfUrl?: string;
  citations?: number;
}

/**
 * Search arXiv for academic papers
 */
async function searchArxiv(query: string, maxResults: number = 10): Promise<AcademicPaper[]> {
  try {
    const baseUrl = 'http://export.arxiv.org/api/query';
    const params = new URLSearchParams({
      search_query: `all:${query}`,
      start: '0',
      max_results: maxResults.toString(),
      sortBy: 'relevance',
      sortOrder: 'descending'
    });

    const response = await fetch(`${baseUrl}?${params}`);
    if (!response.ok) {
      throw new Error(`arXiv API error: ${response.status}`);
    }

    const xmlText = await response.text();
    
    // Basic XML parsing for arXiv results
    const papers: AcademicPaper[] = [];
    const entries = xmlText.match(/<entry>[\s\S]*?<\/entry>/g) || [];
    
    for (const entry of entries) {
      const id = entry.match(/<id>(.*?)<\/id>/)?.[1] || '';
      const title = entry.match(/<title>(.*?)<\/title>/)?.[1]?.trim() || '';
      const summary = entry.match(/<summary>(.*?)<\/summary>/)?.[1]?.trim() || '';
      const published = entry.match(/<published>(.*?)<\/published>/)?.[1] || '';
      
      // Extract authors
      const authorMatches = entry.matchAll(/<author>\s*<name>(.*?)<\/name>\s*<\/author>/g);
      const authors = Array.from(authorMatches).map(match => match[1]);
      
      // Extract categories
      const categoryMatches = entry.matchAll(/<category.*?term="(.*?)"/g);
      const categories = Array.from(categoryMatches).map(match => match[1]);
      
      // Extract PDF link
      const pdfLink = entry.match(/<link.*?title="pdf".*?href="(.*?)"/)?.[1];
      
      papers.push({
        id: id.split('/').pop() || id,
        title,
        authors,
        abstract: summary,
        publishedDate: published,
        source: 'arXiv',
        url: id,
        pdfUrl: pdfLink,
        categories
      });
    }
    
    return papers;
  } catch (error) {
    console.error('[AcademicSources] arXiv search failed:', error);
    return [];
  }
}

/**
 * Search PubMed Central for medical/biological papers
 */
async function searchPubMed(query: string, maxResults: number = 10): Promise<AcademicPaper[]> {
  try {
    // First, search for IDs
    const searchUrl = 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi';
    const searchParams = new URLSearchParams({
      db: 'pmc',
      term: query,
      retmax: maxResults.toString(),
      retmode: 'json',
      sort: 'relevance'
    });

    const searchResponse = await fetch(`${searchUrl}?${searchParams}`);
    if (!searchResponse.ok) {
      throw new Error(`PubMed search error: ${searchResponse.status}`);
    }

    const searchData = await searchResponse.json();
    const ids = searchData.esearchresult?.idlist || [];
    
    if (ids.length === 0) {
      return [];
    }

    // Fetch details for the IDs
    const summaryUrl = 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi';
    const summaryParams = new URLSearchParams({
      db: 'pmc',
      id: ids.join(','),
      retmode: 'json'
    });

    const summaryResponse = await fetch(`${summaryUrl}?${summaryParams}`);
    if (!summaryResponse.ok) {
      throw new Error(`PubMed summary error: ${summaryResponse.status}`);
    }

    const summaryData = await summaryResponse.json();
    const papers: AcademicPaper[] = [];

    for (const id of ids) {
      const article = summaryData.result?.[id];
      if (article) {
        papers.push({
          id: `PMC${id}`,
          title: article.title || '',
          authors: article.authors?.map((a: any) => a.name) || [],
          abstract: '', // Would need additional API call for abstract
          publishedDate: article.pubdate || '',
          source: 'PubMed Central',
          url: `https://www.ncbi.nlm.nih.gov/pmc/articles/PMC${id}/`,
          doi: article.doi
        });
      }
    }

    return papers;
  } catch (error) {
    console.error('[AcademicSources] PubMed search failed:', error);
    return [];
  }
}

/**
 * Search Directory of Open Access Journals
 */
async function searchDOAJ(query: string, maxResults: number = 10): Promise<AcademicPaper[]> {
  try {
    const baseUrl = 'https://doaj.org/api/search/articles';
    const params = new URLSearchParams({
      source: JSON.stringify({
        query: {
          query_string: {
            query: query,
            default_field: '_all'
          }
        },
        size: maxResults
      })
    });

    const response = await fetch(`${baseUrl}?${params}`);
    if (!response.ok) {
      throw new Error(`DOAJ API error: ${response.status}`);
    }

    const data = await response.json();
    const papers: AcademicPaper[] = [];

    for (const hit of data.results || []) {
      const article = hit.bibjson || {};
      papers.push({
        id: hit.id || '',
        title: article.title || '',
        authors: article.author?.map((a: any) => a.name) || [],
        abstract: article.abstract || '',
        publishedDate: article.year || '',
        source: 'DOAJ',
        url: article.link?.[0]?.url || '',
        doi: article.identifier?.find((i: any) => i.type === 'doi')?.id
      });
    }

    return papers;
  } catch (error) {
    console.error('[AcademicSources] DOAJ search failed:', error);
    return [];
  }
}

export const academicSourcesTool: Tool = {
  id: 'academicSources',
  name: 'Academic Sources Search',
  description: 'Search academic and official sources including arXiv, PubMed Central, and DOAJ',
  execute: async (params: AcademicSearchParams): Promise<ToolResult> => {
    const { 
      query, 
      source = 'all', 
      maxResults = 10,
      startDate,
      endDate,
      category
    } = params;
    
    console.log(`[AcademicSources] Searching for: ${query} in ${source}`);
    
    try {
      let allPapers: AcademicPaper[] = [];
      
      // Search selected sources
      if (source === 'all' || source === 'arxiv') {
        const arxivPapers = await searchArxiv(query, maxResults);
        allPapers = allPapers.concat(arxivPapers);
      }
      
      if (source === 'all' || source === 'pubmed') {
        const pubmedPapers = await searchPubMed(query, maxResults);
        allPapers = allPapers.concat(pubmedPapers);
      }
      
      if (source === 'all' || source === 'doaj') {
        const doajPapers = await searchDOAJ(query, maxResults);
        allPapers = allPapers.concat(doajPapers);
      }
      
      // Filter by date if specified
      if (startDate || endDate) {
        const start = startDate ? new Date(startDate) : new Date('1900-01-01');
        const end = endDate ? new Date(endDate) : new Date();
        
        allPapers = allPapers.filter(paper => {
          const paperDate = new Date(paper.publishedDate);
          return paperDate >= start && paperDate <= end;
        });
      }
      
      // Filter by category if specified
      if (category) {
        allPapers = allPapers.filter(paper => 
          paper.categories?.some(cat => 
            cat.toLowerCase().includes(category.toLowerCase())
          )
        );
      }
      
      // Sort by date (newest first)
      allPapers.sort((a, b) => 
        new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime()
      );
      
      // Limit results
      if (source === 'all') {
        allPapers = allPapers.slice(0, maxResults);
      }
      
      console.log(`[AcademicSources] Found ${allPapers.length} papers`);
      
      // Create search suggestions
      const suggestions = [
        'Try searching with more specific keywords',
        'Use author names for targeted results',
        'Include year ranges for temporal filtering',
        'Check specific databases for domain-specific content'
      ];
      
      // Identify available databases for the topic
      const recommendedDatabases: string[] = [];
      if (query.toLowerCase().match(/medical|health|disease|treatment/)) {
        recommendedDatabases.push('PubMed Central', 'DOAJ Medical Journals');
      }
      if (query.toLowerCase().match(/computer|ai|machine learning|algorithm/)) {
        recommendedDatabases.push('arXiv CS', 'ACM Digital Library (subscription required)');
      }
      if (query.toLowerCase().match(/physics|quantum|particle/)) {
        recommendedDatabases.push('arXiv Physics', 'INSPIRE-HEP');
      }
      
      return {
        success: true,
        data: {
          papers: allPapers,
          totalFound: allPapers.length,
          sources: {
            arxiv: allPapers.filter(p => p.source === 'arXiv').length,
            pubmed: allPapers.filter(p => p.source === 'PubMed Central').length,
            doaj: allPapers.filter(p => p.source === 'DOAJ').length
          },
          suggestions,
          recommendedDatabases,
          query,
          searchDate: new Date().toISOString()
        },
        metadata: {
          tool: 'academicSources',
          timestamp: new Date().toISOString(),
          sourcesSearched: source === 'all' ? ['arxiv', 'pubmed', 'doaj'] : [source]
        }
      };
      
    } catch (error) {
      console.error('[AcademicSources] Search failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Academic search failed',
        data: {
          papers: [],
          totalFound: 0,
          sources: { arxiv: 0, pubmed: 0, doaj: 0 },
          suggestions: [
            'Try searching individual databases directly',
            'Check your internet connection',
            'Verify the search query syntax'
          ],
          recommendedDatabases: [],
          query,
          searchDate: new Date().toISOString()
        },
        metadata: {
          tool: 'academicSources',
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }
};