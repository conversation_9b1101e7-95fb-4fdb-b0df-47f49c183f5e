/**
 * Deep Research Orchestrator - Core Implementation
 * 
 * @description
 * Main orchestrator class that coordinates the entire deep research process.
 * Integrates AI SDK v5 streaming, LangChain agent management, and real-time
 * progress tracking with SSE-compatible event streams.
 * 
 * @module lib/ai/deep-research/orchestrator
 */

import { streamText, generateText, tool } from 'ai';
import {
  ResearchPhase,
  ResearchSessionStatus,
  ResearchError,
  AgentStatus,
  ResearchProgressType
} from './types';
import type { 
  DeepResearchRequest,
  DeepResearchOptions,
  DeepResearchResult,
  ResearchContext,
  ResearchProgressUpdate,
  ResearchSession,
  StreamingEventHandler,
  DeepResearchConfig
} from './types';
import { deepResearchConfig } from './config';
import { ResearchSupervisor } from './supervisor';
import { StreamCoordinator } from './streaming/coordinator';
import { ProgressTracker } from './streaming/progress';
import { SessionManager } from './memory/session';
import { KnowledgeBase } from './memory/knowledge';
import { InputValidator } from './validation/input';
import { ModelSelector } from './utils/model-selection';
import { RateLimiter } from './utils/rate-limiter';
import { PerformanceMonitor } from './utils/performance';
import { routerLogger } from '@/lib/logger';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { FactCheckAgent } from './agents/fact-check';

/**
 * Deep Research Orchestrator
 * 
 * Manages the complete deep research workflow including agent coordination,
 * streaming progress updates, and result generation.
 */
export class DeepResearchOrchestrator {
  private config = deepResearchConfig.getConfig();
  private supervisor: ResearchSupervisor;
  private streamCoordinator: StreamCoordinator;
  private progressTracker: ProgressTracker;
  private sessionManager: SessionManager;
  private knowledgeBase: KnowledgeBase;
  private inputValidator: InputValidator;
  private modelSelector: ModelSelector;
  private rateLimiter: RateLimiter;
  private performanceMonitor: PerformanceMonitor;
  private logger = routerLogger;

  constructor() {
    // Initialize core components
    this.supervisor = new ResearchSupervisor();
    this.streamCoordinator = new StreamCoordinator();
    this.progressTracker = new ProgressTracker();
    this.sessionManager = new SessionManager();
    this.knowledgeBase = new KnowledgeBase();
    this.inputValidator = new InputValidator();
    this.modelSelector = new ModelSelector();
    this.rateLimiter = new RateLimiter();
    this.performanceMonitor = new PerformanceMonitor();
  }

  /**
   * Execute deep research with optional streaming
   */
  async research(
    request: DeepResearchRequest,
    eventHandler?: StreamingEventHandler
  ): Promise<DeepResearchResult> {
    const sessionId = uuidv4();
    const timer = this.performanceMonitor.startTimer('deep-research');
    
    try {
      // Validate request
      const validation = await this.inputValidator.validate(request);
      if (!validation.valid) {
        throw new ResearchError(`Invalid request: ${validation.errors.join(', ')}`);
      }

      // Check rate limits
      const rateLimitOk = await this.rateLimiter.checkLimit(
        request.userId || 'anonymous',
        request.context?.user?.plan || 'FREE'
      );
      if (!rateLimitOk) {
        throw new ResearchError('Rate limit exceeded', ResearchPhase.PLANNING);
      }

      // Check cache
      const cached = await this.knowledgeBase.getCachedResult(request.query);
      if (cached && this.isCacheValid(cached, request.options)) {
        this.logger.info('Using cached research result', { query: request.query });
        return cached;
      }

      // Create session
      const session = await this.sessionManager.createSession({
        id: sessionId,
        userId: request.userId,
        query: request.query,
        options: request.options || {},
        status: ResearchSessionStatus.INITIALIZING,
        phase: ResearchPhase.PLANNING,
        progress: 0,
        agents: [],
        tasks: [],
        intermediateResults: [],
        startTime: new Date(),
        errors: [],
        retryCount: 0,
        metadata: {}
      });

      // Execute research with streaming
      if (eventHandler) {
        return await this.streamingResearch(session, request, eventHandler);
      } else {
        return await this.standardResearch(session, request);
      }

    } catch (error) {
      this.logger.error('Deep research failed', error);
      await this.sessionManager.updateSession(sessionId, {
        status: ResearchSessionStatus.FAILED,
        errors: [error as ResearchError]
      });
      throw error;
    } finally {
      timer();
    }
  }

  /**
   * Execute research with streaming progress updates
   */
  async streamingResearch(
    session: ResearchSession,
    request: DeepResearchRequest,
    eventHandler?: StreamingEventHandler
  ): Promise<DeepResearchResult> {
    try {
      // Initialize progress tracking with event handler
      if (eventHandler) {
        this.progressTracker.initialize(session.id, undefined, eventHandler);
      }
          
          // Phase 1: Planning
          await this.progressTracker.updatePhase(session.id, ResearchPhase.PLANNING, 'Analyzing query and planning research strategy');
          const researchPlan = await this.supervisor.planResearch(request.query, request.options);
          await this.progressTracker.updateProgress(session.id, 20, 'Research plan created');

          // Phase 2: Agent Coordination
          await this.progressTracker.updatePhase(session.id, ResearchPhase.SEARCH, 'Deploying research agents');
          const compatibleConfig: DeepResearchConfig = {
            maxConcurrentSessions: this.config.systemLimits.maxConcurrentSessions,
            maxConcurrentAgents: this.config.systemLimits.maxConcurrentAgents,
            defaultTimeout: this.config.systemLimits.defaultTimeoutMs,
            maxTimeout: this.config.systemLimits.maxTimeoutMs,
            defaultQuality: 'comprehensive',
            defaultDepth: 'moderate',
            models: this.config.models,
            tools: [],
            enableCaching: true,
            cacheTimeout: 3600,
            enableParallelization: true,
            enableMetrics: true,
            enableDetailedLogging: false,
            rateLimits: {
              perUser: 10,
              perSession: 50,
              global: 100
            }
          };
          const agents = await this.supervisor.spawnAgents(researchPlan, compatibleConfig);
          await this.progressTracker.updateProgress(session.id, 30, `${agents.length} research agents deployed`);

          // Phase 3: Parallel Research Execution
          const findings = await this.executeParallelResearch(session, agents, eventHandler);
          await this.progressTracker.updateProgress(session.id, 60, 'Research data collected');

          // Phase 4: Analysis
          await this.progressTracker.updatePhase(session.id, ResearchPhase.ANALYSIS, 'Analyzing research findings');
          const analysis = await this.supervisor.analyzeFindings(findings);
          await this.progressTracker.updateProgress(session.id, 70, 'Analysis complete');

          // Phase 5: Fact-Checking and Validation
          await this.progressTracker.updatePhase(session.id, ResearchPhase.FACT_CHECK, 'Verifying facts and validating sources');
          const factCheckResult = await this.performFactChecking(analysis, findings, eventHandler);
          await this.progressTracker.updateProgress(session.id, 85, 'Fact-checking complete');

          // Phase 6: Revision if needed
          let finalAnalysis = analysis;
          if (factCheckResult.requiresRevision) {
            await this.progressTracker.updatePhase(session.id, ResearchPhase.REVISION, 'Revising content based on fact-checking');
            finalAnalysis = await this.reviseAnalysis(analysis, factCheckResult, findings);
            await this.progressTracker.updateProgress(session.id, 90, 'Revisions complete');
          }

          // Phase 7: Report Generation (Delivery)
          await this.progressTracker.updatePhase(session.id, ResearchPhase.REPORT, 'Generating comprehensive report');
          const report = await this.generateFinalReport(request, finalAnalysis, findings, factCheckResult);
          await this.progressTracker.updateProgress(session.id, 100, 'Research complete');

          // Send final result through event handler if available
          if (eventHandler) {
            await eventHandler.onComplete?.(report);
          }

          // Cache result
          await this.knowledgeBase.cacheResult(request.query, report);

          // Update session
          await this.sessionManager.updateSession(session.id, {
            status: ResearchSessionStatus.COMPLETED,
            finalResult: report,
            endTime: new Date()
          });

          return report;

        } catch (error) {
          if (eventHandler) {
            await eventHandler.onError?.(error as Error);
          }
          await this.handleStreamingError(session.id, error as Error);
          throw error;
        }
  }

  /**
   * Execute standard research without streaming
   */
  async standardResearch(
    session: ResearchSession,
    request: DeepResearchRequest
  ): Promise<DeepResearchResult> {
    // Similar to streaming but without progress updates
    const researchPlan = await this.supervisor.planResearch(request.query, request.options);
    const compatibleConfig: DeepResearchConfig = {
      maxConcurrentSessions: this.config.systemLimits.maxConcurrentSessions,
      maxConcurrentAgents: this.config.systemLimits.maxConcurrentAgents,
      defaultTimeout: this.config.systemLimits.defaultTimeoutMs,
      maxTimeout: this.config.systemLimits.maxTimeoutMs,
      defaultQuality: 'comprehensive',
      defaultDepth: 'moderate',
      models: this.config.models,
      tools: [],
      enableCaching: true,
      cacheTimeout: 3600,
      enableParallelization: true,
      enableMetrics: true,
      enableDetailedLogging: false,
      rateLimits: {
        perUser: 10,
        perSession: 50,
        global: 100
      }
    };
    const agents = await this.supervisor.spawnAgents(researchPlan, compatibleConfig);
    const findings = await this.executeStandardResearch(session, agents);
    const analysis = await this.supervisor.analyzeFindings(findings);
    
    // Perform fact-checking
    const factCheckResult = await this.performFactChecking(analysis, findings);
    
    // Revise if needed
    let finalAnalysis = analysis;
    if (factCheckResult.requiresRevision) {
      finalAnalysis = await this.reviseAnalysis(analysis, factCheckResult, findings);
    }
    
    // Generate final report
    const report = await this.generateFinalReport(request, finalAnalysis, findings, factCheckResult);
    
    await this.knowledgeBase.cacheResult(request.query, report);
    await this.sessionManager.updateSession(session.id, {
      status: ResearchSessionStatus.COMPLETED,
      finalResult: report,
      endTime: new Date()
    });
    
    return report;
  }

  /**
   * Execute parallel research with streaming updates
   */
  private async executeParallelResearch(
    session: ResearchSession,
    agents: any[],
    eventHandler?: StreamingEventHandler
  ): Promise<any[]> {
    const findings: any[] = [];
    const totalAgents = agents.length;
    let completedAgents = 0;

    // Execute agents in parallel with progress tracking
    await Promise.all(
      agents.map(async (agent, index) => {
        try {
          // Update agent status
          if (eventHandler && eventHandler.onAgent) {
            await eventHandler.onAgent(agent, {
              id: crypto.randomUUID(),
              agentId: agent.id,
              type: agent.type,
              priority: 'normal' as any,
              status: AgentStatus.INITIALIZING,
              query: `Starting ${agent.type} agent`,
              startTime: new Date()
            });
          }

          // Execute agent research
          const agentFindings = await agent.research();
          findings.push({
            agentId: agent.id,
            agentType: agent.type,
            findings: agentFindings
          });

          // Update progress
          completedAgents++;
          const progress = 30 + Math.floor((completedAgents / totalAgents) * 30);
          await this.progressTracker.updateProgress(
            session.id,
            progress,
            `Agent ${agent.id} completed (${completedAgents}/${totalAgents})`
          );

          // Stream agent findings
          if (eventHandler && eventHandler.onAgent) {
            await eventHandler.onAgent(agent, {
              id: crypto.randomUUID(),
              agentId: agent.id,
              type: agent.type,
              priority: 'normal' as any,
              status: AgentStatus.COMPLETED,
              query: `${agent.type} agent completed`,
              endTime: new Date(),
              result: {
                success: true,
                data: agentFindings
              }
            });
          }

        } catch (error) {
          this.logger.error(`Agent ${agent.id} failed`, error);
          if (eventHandler && eventHandler.onAgent) {
            await eventHandler.onAgent(agent, {
              id: crypto.randomUUID(),
              agentId: agent.id,
              type: agent.type,
              priority: 'normal' as any,
              status: AgentStatus.FAILED,
              query: `${agent.type} agent failed`,
              endTime: new Date(),
              error: new ResearchError(
                (error as Error).message,
                ResearchPhase.SEARCH,
                agent.id
              )
            });
          }
        }
      })
    );

    return findings;
  }

  /**
   * Execute standard research without streaming
   */
  private async executeStandardResearch(
    session: ResearchSession,
    agents: any[]
  ): Promise<any[]> {
    const findings: any[] = [];
    
    await Promise.all(
      agents.map(async (agent) => {
        try {
          const agentFindings = await agent.research();
          findings.push({
            agentId: agent.id,
            agentType: agent.type,
            findings: agentFindings
          });
        } catch (error) {
          this.logger.error(`Agent ${agent.id} failed`, error);
        }
      })
    );

    return findings;
  }

  /**
   * Perform fact-checking on analysis and findings
   */
  private async performFactChecking(
    analysis: any,
    findings: any[],
    eventHandler?: StreamingEventHandler
  ): Promise<any> {
    try {
      // Create a fact-check agent
      const factCheckAgent = new FactCheckAgent(
        'fact-checker-' + Date.now(),
        'Fact Checker',
        await this.modelSelector.selectModel('fact-check')
      );

      // Prepare content for fact-checking
      const contentToCheck = {
        type: 'check-report' as const,
        content: JSON.stringify({
          summary: analysis.summary || analysis.executiveSummary,
          keyInsights: analysis.keyInsights || [],
          findings: findings.map(f => ({
            agentId: f.agentId,
            findings: f.findings
          }))
        }),
        sources: this.extractSources(findings),
        context: `Research analysis for: ${analysis.query || 'unknown query'}`
      };

      // Execute fact-checking
      const factCheckResult = await factCheckAgent.execute(contentToCheck);
      
      // Also validate sources using the source validator
      const sourcesToValidate = this.extractSources(findings);
      let sourceValidationResult = null;
      
      if (sourcesToValidate.length > 0) {
        try {
          const sourceValidator = await import('./tools/source-validator');
          const validationResult = await sourceValidator.sourceValidatorTool.execute({
            sources: sourcesToValidate.map(s => ({
              url: s.url,
              title: s.title,
              content: s.snippet || s.content,
              publishedDate: s.publishedDate || s.date,
              author: s.author,
              domain: s.domain
            })),
            topic: analysis.query || 'general research',
            minCredibilityScore: 6
          });
          
          if (validationResult.success && validationResult.data) {
            sourceValidationResult = validationResult.data;
          }
        } catch (error) {
          this.logger.warn('Source validation failed', { error: error instanceof Error ? error.message : 'Unknown error' });
        }
      }
      
      if (eventHandler && eventHandler.onProgress) {
        await eventHandler.onProgress({
          type: ResearchProgressType.AGENT_PROGRESS,
          timestamp: new Date(),
          phase: ResearchPhase.FACT_CHECK,
          progress: 100,
          currentStep: `Fact-checking complete: ${factCheckResult.data?.overallVerificationStatus || 'completed'}`
        });
      }

      // Combine fact-check and source validation results
      const combinedResult = {
        ...(factCheckResult.data || {}),
        requiresRevision: factCheckResult.data?.requiresRevision || false,
        overallConfidence: factCheckResult.data?.overallConfidence || 0.8,
        issues: [...(factCheckResult.data?.issues || []), ...(sourceValidationResult?.redFlags || [])],
        recommendations: [...(factCheckResult.data?.recommendations || []), ...(sourceValidationResult?.recommendations || [])],
        sourceValidation: sourceValidationResult
      };

      return combinedResult;

    } catch (error) {
      this.logger.error('Fact-checking failed', error);
      return {
        requiresRevision: false,
        overallConfidence: 0.7,
        issues: ['Fact-checking process encountered an error'],
        recommendations: []
      };
    }
  }

  /**
   * Revise analysis based on fact-checking results
   */
  private async reviseAnalysis(
    originalAnalysis: any,
    factCheckResult: any,
    findings: any[]
  ): Promise<any> {
    try {
      const model = await this.modelSelector.selectModel('analysis');
      
      // Use AI to revise the analysis based on fact-check feedback
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 45000);
      
      const result = await generateText({
        model,
        messages: [
          {
            role: 'system',
            content: `You are a research revision expert. Revise the analysis based on fact-checking feedback.
            
Your task:
1. Address all issues identified in fact-checking
2. Remove or correct false claims
3. Strengthen weak claims with better evidence
4. Improve clarity and accuracy
5. Maintain the overall narrative flow
6. Express confidence levels in natural language (not percentages)`
          },
          {
            role: 'user',
            content: `Revise this analysis based on fact-check results:

Original Analysis:
${JSON.stringify(originalAnalysis, null, 2)}

Fact-Check Results:
${JSON.stringify(factCheckResult, null, 2)}

Revision Notes:
${factCheckResult.revisionNotes?.join('\n') || 'No specific notes'}

Please provide the revised analysis maintaining the same structure but with improved accuracy.`
          }
        ],
        maxOutputTokens: 8000,
        temperature: 0.2,
        abortSignal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      // Parse the revised analysis
      try {
        const revisedAnalysis = JSON.parse(result.text);
        return {
          ...originalAnalysis,
          ...revisedAnalysis,
          wasRevised: true,
          revisionReason: 'Fact-checking identified issues requiring correction',
          confidenceImproved: true
        };
      } catch (parseError) {
        // If parsing fails, merge the improvements narratively
        return {
          ...originalAnalysis,
          summary: result.text,
          wasRevised: true,
          revisionReason: 'Fact-checking improvements applied'
        };
      }
      
    } catch (error) {
      this.logger.error('Analysis revision failed', error);
      // Return original analysis if revision fails
      return originalAnalysis;
    }
  }

  /**
   * Generate final research report
   */
  private async generateFinalReport(
    request: DeepResearchRequest,
    analysis: any,
    findings: any[],
    factCheckResult?: any
  ): Promise<DeepResearchResult> {
    const reportModel = await this.modelSelector.selectModel(
      'report',
      request.context?.user?.plan
    );

    // Use generateText for more reliable processing in orchestrator context
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 1 minute timeout for reports
    
    let reportText: string;
    try {
      const result = await generateText({
        model: reportModel,
        messages: [
          {
            role: 'system',
            content: `You are an expert research report writer. Create a comprehensive, essay-style report that reads naturally and professionally.

CRITICAL INSTRUCTIONS:
1. Write in a natural, academic essay style - no confidence scores as percentages
   - DO include factual percentages from sources (e.g., "Research found that 73% of participants...")
   - DON'T express your confidence as percentages (e.g., "We are 85% confident that...")
2. Express certainty through language:
   - High confidence: "The evidence clearly demonstrates...", "Multiple authoritative sources confirm...", "Research consistently shows..."
   - Medium confidence: "The available data suggests...", "Current research indicates...", "Evidence points to..."
   - Low confidence: "Preliminary findings suggest...", "Limited data indicates...", "Further research is needed to confirm..."
3. Always cite sources naturally within the text
4. Address any contradictions or debates in the field
5. Acknowledge limitations where appropriate
6. Write as if for an educated audience seeking thorough understanding`
          },
          {
            role: 'user',
            content: `Generate a comprehensive research report for: "${request.query}"

Analysis Data: ${JSON.stringify(analysis)}
Fact-Check Results: ${JSON.stringify(factCheckResult || {})}
Confidence Indicators: ${JSON.stringify({
  overallConfidence: factCheckResult?.overallConfidence || analysis.overallConfidence || 0.8,
  requiresRevision: factCheckResult?.requiresRevision || false,
  verificationStatus: factCheckResult?.overallVerificationStatus || 'verified'
})}

Write a well-structured essay-style report that:
1. Opens with a compelling introduction
2. Presents findings with appropriate confidence language
3. Integrates sources naturally
4. Addresses any controversies or limitations
5. Concludes with actionable insights

Remember: Express confidence through word choice, not numbers. Make it read like a professional research paper or detailed analysis article.`
          }
        ],
        maxOutputTokens: 16000,
        temperature: 0.3,
        abortSignal: controller.signal
      });
      
      clearTimeout(timeoutId);
      reportText = result.text;
    } catch (error) {
      clearTimeout(timeoutId);
      this.logger.error('Report generation failed', { error });
      reportText = `# Research Report: ${request.query}\n\nReport generation encountered an error. Please try again.`;
    }

    // Calculate internal confidence metrics (not exposed as percentages to users)
    const internalConfidence = factCheckResult?.overallConfidence || 
                              analysis.overallConfidence || 
                              this.calculateConfidence(findings);
    
    // Extract and verify citations
    const extractedCitations = await this.extractAndVerifyCitations(reportText, findings);
    
    return {
      id: uuidv4(),
      query: request.query,
      success: true,
      analysis: analysis.summary || reportText,
      summary: analysis.executiveSummary || this.extractSummary(reportText),
      report: reportText,
      sources: this.extractSources(findings),
      findings: this.processFindings(findings),
      citations: extractedCitations,
      confidence: internalConfidence, // Internal use only - not displayed to users
      reliability: this.calculateReliability(findings),
      completeness: this.assessCompleteness(findings),
      executionTime: Date.now() - (typeof request.sessionId === 'number' ? request.sessionId : Date.now()),
      tokensUsed: this.calculateTokenUsage(findings),
      agentsUsed: this.getAgentUsageInfo(findings),
      sessionId: request.sessionId || uuidv4(),
      userId: request.userId,
      timestamp: new Date(),
      errors: factCheckResult?.issues || [],
      warnings: factCheckResult?.recommendations || [],
      metadata: {
        researchQuality: request.options?.researchQuality || 'comprehensive',
        analysisDepth: request.options?.analysisDepth || 'moderate',
        wasFactChecked: true,
        wasRevised: analysis.wasRevised || false,
        verificationStatus: factCheckResult?.overallVerificationStatus || 'unverified',
        // Internal metrics for quality tracking
        internalMetrics: {
          confidenceScore: internalConfidence,
          factCheckPassed: !factCheckResult?.requiresRevision,
          sourceCredibility: factCheckResult?.sourceValidation?.averageCredibilityScore || 0
        }
      }
    };
  }

  /**
   * Handle streaming errors gracefully
   */
  private async handleStreamingError(
    sessionId: string,
    error: Error
  ): Promise<void> {
    this.logger.error('Streaming research error', error);

    await this.sessionManager.updateSession(sessionId, {
      status: ResearchSessionStatus.FAILED,
      errors: [error as ResearchError]
    });
  }

  // Helper methods
  private isCacheValid(cached: DeepResearchResult, options?: DeepResearchOptions): boolean {
    const cacheAge = Date.now() - cached.timestamp.getTime();
    const maxAge = options?.enableCaching ? this.config.quality.optimization.cacheTimeoutMs : 0;
    return cacheAge < maxAge;
  }

  private isRetryableError(error: Error): boolean {
    return error.message.includes('timeout') || error.message.includes('rate limit');
  }

  private extractSummary(report: string): string {
    // Extract executive summary from report
    const summaryMatch = report.match(/Executive Summary[:\n]+(.*?)(?=\n\n|\n#|$)/s);
    return summaryMatch ? summaryMatch[1].trim() : report.substring(0, 500) + '...';
  }

  private extractSources(findings: any[]): any[] {
    // Extract and deduplicate sources from findings
    const sources = new Map();
    findings.forEach(f => {
      if (f.findings?.sources) {
        f.findings.sources.forEach((source: any) => {
          if (!sources.has(source.url)) {
            sources.set(source.url, source);
          }
        });
      }
    });
    return Array.from(sources.values());
  }

  private processFindings(findings: any[]): any[] {
    // Process and structure findings
    return findings.flatMap(f => f.findings?.keyFindings || []);
  }

  private generateCitations(findings: any[]): any[] {
    // Generate citations from findings
    const citations: any[] = [];
    findings.forEach((f, index) => {
      if (f.findings?.sources) {
        f.findings.sources.forEach((source: any) => {
          citations.push({
            id: `cite-${index}-${citations.length}`,
            source,
            citationText: `[${index + 1}] ${source.title}. ${source.url}`,
            format: 'simple',
            inTextReference: `[${index + 1}]`,
            usageCount: 1
          });
        });
      }
    });
    return citations;
  }

  private calculateConfidence(findings: any[]): number {
    // Calculate overall confidence score
    const confidences = findings.map(f => f.findings?.confidence || 0.5);
    return confidences.reduce((a, b) => a + b, 0) / confidences.length;
  }

  private calculateReliability(findings: any[]): number {
    // Calculate source reliability
    const reliabilities = findings.map(f => f.findings?.reliability || 0.5);
    return reliabilities.reduce((a, b) => a + b, 0) / reliabilities.length;
  }

  private assessCompleteness(findings: any[]): number {
    // Assess research completeness
    const hasMultipleSources = findings.length >= 3;
    const hasDiverseTypes = new Set(findings.map(f => f.agentType)).size >= 2;
    const hasDepth = findings.some(f => f.findings?.analysisDepth === 'deep');
    
    let score = 0.5;
    if (hasMultipleSources) score += 0.2;
    if (hasDiverseTypes) score += 0.2;
    if (hasDepth) score += 0.1;
    
    return Math.min(score, 1.0);
  }

  private calculateTokenUsage(findings: any[]): number {
    // Calculate total tokens used
    return findings.reduce((total, f) => total + (f.findings?.tokensUsed || 0), 0);
  }

  private getAgentUsageInfo(findings: any[]): any[] {
    // Get agent usage information
    return findings.map(f => ({
      agentId: f.agentId,
      agentType: f.agentType,
      tasksCompleted: 1,
      executionTime: f.findings?.executionTime || 0,
      tokensUsed: f.findings?.tokensUsed || 0,
      successRate: f.findings ? 1.0 : 0.0
    }));
  }

  /**
   * Extract and verify citations from the report
   */
  private async extractAndVerifyCitations(
    reportText: string,
    findings: any[]
  ): Promise<any[]> {
    try {
      const model = await this.modelSelector.selectModel('analysis');
      const allSources = this.extractSources(findings);
      
      // Use AI to extract citations from the report
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);
      
      const result = await generateText({
        model,
        messages: [
          {
            role: 'system',
            content: `You are a citation extraction expert. Extract all citations and source references from the research report.

Extract:
1. Direct quotes with their sources
2. Paraphrased information with attribution
3. Statistical claims with sources
4. Any other referenced material

For each citation, identify:
- The claim or quote
- The source referenced
- The type (direct quote, paraphrase, statistic, etc.)
- Whether it can be verified against the provided sources

Return a JSON array of citation objects.`
          },
          {
            role: 'user',
            content: `Extract citations from this report:

${reportText}

Available sources for verification:
${JSON.stringify(allSources.map(s => ({ url: s.url, title: s.title })), null, 2)}

Return JSON array with format:
[{
  "text": "quoted or referenced text",
  "type": "quote|paraphrase|statistic|reference",
  "sourceReference": "how it's cited in the text",
  "verifiedSource": { url, title } or null,
  "verified": boolean
}]`
          }
        ],
        maxOutputTokens: 6000,
        temperature: 0.2,
        abortSignal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      // Parse extracted citations
      try {
        const jsonMatch = result.text.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          const citations = JSON.parse(jsonMatch[0]);
          
          // Enhance citations with additional metadata
          return citations.map((citation: any, index: number) => ({
            id: `cite-${index + 1}`,
            ...citation,
            citationText: citation.sourceReference || `[${index + 1}]`,
            format: 'inline',
            inTextReference: `[${index + 1}]`,
            verificationStatus: citation.verified ? 'verified' : 'unverified',
            usageCount: 1
          }));
        }
      } catch (parseError) {
        this.logger.warn('Failed to parse citations', { error: parseError instanceof Error ? parseError.message : 'Unknown error' });
      }
      
      // Fallback to basic citation generation
      return this.generateCitations(findings);
      
    } catch (error) {
      this.logger.error('Citation extraction failed', { error: error instanceof Error ? error.message : 'Unknown error' });
      // Fallback to basic citation generation
      return this.generateCitations(findings);
    }
  }
}