/**
 * Simple Deep Research Test - Minimal implementation for testing
 * 
 * AI SDK v5 COMPLETE SYNTAX REFERENCE FOR MESSAGE PERSISTENCE:
 * 
 * CLIENT SIDE (useChat hook):
 * ```typescript
 * const { messages, input, handleSubmit, status, append } = useChat({
 *   id: conversationId,              // REQUIRED for persistence - unique conversation ID
 *   api: '/api/chat/endpoint',       // API endpoint URL
 *   sendExtraMessageFields: true,    // CRITICAL - sends message IDs & timestamps
 *   body: {                          // Additional data sent with each request
 *     id: conversationId,            // Send conversation ID in body too
 *     manualModel: 'gpt-4o-mini',    // Any custom fields
 *   },
 *   initialMessages: [],             // Load previous messages here
 * })
 * ```
 * 
 * SERVER SIDE (streamText with persistence):
 * ```typescript
 * // 1. Import required functions
 * import { 
 *   streamText,                      // Core streaming function
 *   convertToModelMessages,          // Convert UI messages to model format
 *   appendResponseMessages,          // Helper to merge user + AI messages
 *   generateId                       // Generate unique IDs
 * } from 'ai'
 * 
 * // 2. Extract data from request
 * const { messages, id } = await req.json()
 * //      ^^^^^^^^^  ^^^
 * //      UI format  conversation ID from client
 * 
 * // 3. Stream with onFinish callback
 * const result = streamText({
 *   model: openai('gpt-4o-mini'),
 *   messages: convertToModelMessages(messages), // CRITICAL - convert format
 *   system: 'You are helpful',
 *   onFinish: async ({ text, usage, response }) => {
 *     // response.messages contains AI response in CoreMessage format
 *     // text contains the full text response
 *     // usage contains token counts
 *     
 *     // Save using appendResponseMessages helper
 *     const allMessages = appendResponseMessages({
 *       messages: messages,              // Original messages (UI format)
 *       responseMessages: response.messages  // AI response (Core format)
 *     })
 *     
 *     // Now save allMessages to database
 *     await saveChat({ id, messages: allMessages })
 *   }
 * })
 * 
 * // 4. Return streaming response
 * return result.toUIMessageStreamResponse()
 * ```
 * 
 * MESSAGE FORMATS:
 * 
 * UIMessage (from useChat):
 * {
 *   id: 'msg-123',                    // Unique message ID
 *   role: 'user' | 'assistant',       // Message role
 *   content: 'Hello',                 // Text content
 *   createdAt: Date,                  // Timestamp
 *   // v5 parts array for multimodal:
 *   parts?: [
 *     { type: 'text', text: 'Hello' },
 *     { type: 'image', image: 'data:...' },
 *     { type: 'tool-invocation', toolName: '...', args: {...} }
 *   ]
 * }
 * 
 * CoreMessage (for model):
 * {
 *   role: 'user' | 'assistant' | 'system',
 *   content: 'text' | [                // Can be string or parts array
 *     { type: 'text', text: '...' },
 *     { type: 'image', image: '...' }
 *   ]
 * }
 * 
 * PERSISTENCE HELPERS:
 * 
 * appendResponseMessages(): Merges user messages + AI response
 * - Input: { messages: UIMessage[], responseMessages: CoreMessage[] }
 * - Output: UIMessage[] with all messages combined
 * 
 * convertToModelMessages(): Converts UI to model format
 * - Input: UIMessage[]
 * - Output: CoreMessage[]
 * 
 * LOADING PREVIOUS MESSAGES:
 * ```typescript
 * // In your page component
 * const messages = await loadChat(conversationId)
 * 
 * <Chat initialMessages={messages} />
 * 
 * // In Chat component
 * const { messages } = useChat({
 *   initialMessages: props.initialMessages,
 *   sendExtraMessageFields: true
 * })
 * ```
 * 
 * CRITICAL GOTCHAS:
 * 1. Always use convertToModelMessages() before streamText
 * 2. Always set sendExtraMessageFields: true on client
 * 3. Store messages in UIMessage format (with id, createdAt)
 * 4. Use appendResponseMessages() to merge messages properly
 * 5. Pass conversation ID both in useChat id prop AND body
 */

// v5: Core imports from 'ai' package - streamText for streaming, tool for tool definitions, convertToModelMessages for message format conversion
import { streamText, tool, convertToModelMessages, generateId } from 'ai'
import type { UIMessage, CoreMessage } from 'ai'
// v5: Model provider import - use @ai-sdk/[provider] packages
import { openai } from '@ai-sdk/openai'
import { z } from 'zod'
import { NextRequest } from 'next/server'
import type { 
  DeepResearchToolResponse, 
  ResearchStartedResponse 
} from '@/types/deep-research'
import { ResearchSessionStatus } from '@/lib/ai/deep-research/types'
import { getDeepResearchPrompt } from '@/lib/ai/deep-research/prompts/system-prompts'

// v5: Replacement for removed appendResponseMessages function
function appendResponseMessages({
  messages,
  responseMessages
}: {
  messages: UIMessage[]
  responseMessages: CoreMessage[]
}): UIMessage[] {
  const result = [...messages]
  
  for (const responseMsg of responseMessages) {
    const uiMessage: UIMessage = {
      id: generateId(),
      role: responseMsg.role as 'user' | 'assistant' | 'system',
      parts: []
    }
    
    if (typeof responseMsg.content === 'string') {
      uiMessage.parts.push({
        type: 'text',
        text: responseMsg.content
      })
    } else if (Array.isArray(responseMsg.content)) {
      for (const part of responseMsg.content) {
        if (part.type === 'text') {
          uiMessage.parts.push({
            type: 'text',
            text: part.text
          })
        }
      }
      // In v5, content is derived from parts, not stored separately
    }
    
    result.push(uiMessage)
  }
  
  return result
}

// v5: Tool definition using tool() function - replaces old function calling syntax
const deepResearchTool = tool({
  description: 'Start deep research on a topic', // v5: description field for tool
  parameters: z.object({ // v5: Zod schema for parameter validation
    query: z.string().describe('The research query')
  }),
  // v5: execute function replaces old function call handler - receives validated params and context
  async execute({ query }, { toolCallId, messages }): Promise<DeepResearchToolResponse> {
    const timestamp = new Date().toISOString()
    
    // Log the full tool call with context
    console.log(`[${timestamp}] 🔬 Deep Research Tool Called:`, {
      toolCallId,
      query,
      messagesCount: messages.length,
      lastMessage: (() => {
        const lastMsg = messages[messages.length - 1];
        if (!lastMsg) return 'No messages';
        // AI SDK v5 messages have content property, not parts
        if ('content' in lastMsg && typeof lastMsg.content === 'string') {
          return lastMsg.content.substring(0, 100) + '...';
        }
        return 'Non-text content';
      })()
    })
    
    // Enhance query with current date context
    const currentDate = new Date()
    const dateStr = currentDate.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
    
    // Check if query mentions outdated dates (like September 2023) and provide context
    const enhancedQuery = query.toLowerCase().includes('2023') || query.toLowerCase().includes('september 2023')
      ? `${query} (Important: Today's date is ${dateStr}. Please provide the most current and up-to-date information available as of ${currentDate.getFullYear()}, not from 2023)`
      : `${query} (Current date: ${dateStr})`
    
    console.log(`[${timestamp}] 📅 Enhanced query with date context:`, {
      originalQuery: query,
      enhancedQuery,
      currentDate: dateStr
    })
    
    // Start actual research using the queue system immediately
    const sessionId = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    console.log(`[${timestamp}] 🚀 Starting deep research via queue:`, {
      toolCallId,
      sessionId,
      query: enhancedQuery
    })
    
    try {
      // Import the queue system
      const { deepResearchQueue } = await import('@/lib/ai/deep-research/queue/job-queue')
      
      // Enqueue the research job
      await deepResearchQueue.enqueue({
        id: sessionId,
        userId: 'test-user', // For testing
        request: {
          query: enhancedQuery,
          options: {
            depth: 'comprehensive', // Default to comprehensive research
            maxUrls: 50,
            model: 'auto',
            searchOptions: {
              includeImages: false
            }
          },
          sessionId
        },
        priority: 'normal',
        metadata: {
          testMode: true,
          originalQuery: query
        }
      })
      
      console.log(`[${timestamp}] ✅ Research job enqueued:`, {
        sessionId,
        queue: 'deepResearchQueue'
      })
    } catch (error) {
      console.error(`[${timestamp}] ❌ Failed to enqueue research job:`, error)
      // Fall back to mock response if queue fails
    }
    
    const response: ResearchStartedResponse = {
      type: 'research_started',
      sessionId,
      message: `Starting deep research on: "${query}". This will analyze 50+ sources.`,
      estimatedTime: 300
    }
    
    return response
  }
})

// Check research status tool
const checkResearchStatus = tool({
  description: 'Check the status of an ongoing deep research session',
  parameters: z.object({
    sessionId: z.string().describe('The session ID to check status for')
  }),
  async execute({ sessionId }) {
    const timestamp = new Date().toISOString()
    
    console.log(`[${timestamp}] 🔍 Checking research status:`, { sessionId })
    
    try {
      // Import the queue system
      const { deepResearchQueue } = await import('@/lib/ai/deep-research/queue/job-queue')
      
      // Get job status from queue
      const jobStatus = await deepResearchQueue.getJobStatus(sessionId)
      
      if (jobStatus) {
        console.log(`[${timestamp}] 📊 Job status found:`, {
          sessionId,
          status: jobStatus.status,
          progress: jobStatus.progress,
          phase: jobStatus.currentPhase,
          metadata: jobStatus.metadata
        })
        
        // Map queue status to user-friendly format
        const statusMap: Record<string, string> = {
          'pending': 'Waiting in queue',
          'processing': 'Research in progress',
          'completed': 'Research completed',
          'failed': 'Research failed',
          'cancelled': 'Research cancelled'
        }
        
        // Extract source counts from metadata or phase message
        let sourcesAnalyzed = 0
        let totalSources = undefined
        
        // Check if metadata has sourcesFound (for completed jobs)
        if (jobStatus.metadata?.sourcesFound) {
          sourcesAnalyzed = jobStatus.metadata.sourcesFound
        }
        
        // Parse source counts from currentPhase if it contains the pattern [X/Y]
        const phaseMatch = jobStatus.currentPhase?.match(/\[(\d+)\/(\d+)\]/)
        if (phaseMatch) {
          sourcesAnalyzed = parseInt(phaseMatch[1])
          totalSources = parseInt(phaseMatch[2])
        }
        
        // For web-search phase, extract from progress message
        if (jobStatus.currentPhase?.includes('web-search') && jobStatus.currentPhase?.includes('Found')) {
          const foundMatch = jobStatus.currentPhase.match(/Found (\d+) sources/)
          if (foundMatch) {
            sourcesAnalyzed = Math.max(sourcesAnalyzed, parseInt(foundMatch[1]))
          }
        }
        
        return {
          type: 'status_update',
          sessionId,
          status: jobStatus.status,
          progress: jobStatus.progress || 0,
          currentPhase: jobStatus.currentPhase || statusMap[jobStatus.status] || 'Unknown',
          sourcesAnalyzed: sourcesAnalyzed || undefined,
          totalSources: totalSources,
          message: jobStatus.status === ResearchSessionStatus.COMPLETED 
            ? `Research completed successfully! Analyzed ${sourcesAnalyzed || 'multiple'} sources.`
            : jobStatus.status === ResearchSessionStatus.RUNNING
            ? `Research is ${jobStatus.progress || 0}% complete. ${jobStatus.currentPhase || 'Processing...'}`
            : statusMap[jobStatus.status] || 'Checking status...',
          metadata: jobStatus.metadata
        }
      }
    } catch (error) {
      console.error(`[${timestamp}] ❌ Failed to check job status:`, error)
    }
    
    // Fallback to mock status if queue check fails
    return {
      type: 'status_update',
      sessionId,
      status: 'processing',
      progress: 35,
      currentPhase: 'Analyzing search results',
      sourcesAnalyzed: 23,
      totalSources: 65,
      message: 'Research is progressing well. Currently analyzing sources...'
    }
  }
})

export async function POST(request: NextRequest) {
  const requestId = `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  const timestamp = new Date().toISOString()

  console.log(`[${timestamp}] 🔬 DEEP RESEARCH SIMPLE TEST ROUTE CALLED [${requestId}]`)
  console.log(`[${timestamp}] 🔬 Deep research route - will start research immediately`)

  try {
    const body = await request.json()
    
    // Log the complete incoming request
    console.log(`[${timestamp}] 📨 DEEP RESEARCH REQUEST [${requestId}]:`, {
      requestId,
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers.entries()),
      bodyKeys: Object.keys(body),
      messagesCount: body.messages?.length || 0,
      body: JSON.stringify(body, null, 2)
    })
    
    if (!body.messages || !Array.isArray(body.messages)) {
      console.log(`[${timestamp}] ❌ Invalid request [${requestId}]: missing messages array`)
      return new Response(
        JSON.stringify({ error: 'Invalid request: messages array required' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const { messages: incomingMessages, id: conversationId, manualModel } = body // v5: Extract conversation ID for persistence
    
    console.log(`[${timestamp}] 📥 Conversation details:`, {
      conversationId,
      hasConversationId: !!conversationId,
      messageCount: incomingMessages?.length || 0,
      manualModel
    })
    
    // v5: Use incoming messages (useChat sends full history)
    // Ensure messages is always an array
    const messages = Array.isArray(incomingMessages) ? incomingMessages : []
    
    console.log(`[${timestamp}] 📝 Messages after validation:`, {
      isArray: Array.isArray(messages),
      length: messages.length,
      firstMessage: messages[0] || null,
      messages: JSON.stringify(messages)
    })
    
    // Log if conversation exists for debugging
    if (conversationId) {
      try {
        const { conversationExists } = await import('@/lib/api/chat/deep-research/chat-store')
        const exists = await conversationExists(conversationId)
        console.log(`[${timestamp}] 📚 Conversation ${conversationId} exists: ${exists}`)
      } catch (error) {
        console.error(`[${timestamp}] ❌ Failed to check conversation:`, error)
      }
    }
    
    // Log all messages being sent to the AI
    console.log(`[${timestamp}] 🤖 SENDING TO AI [${requestId}]:`, {
      requestId,
      model: 'openai/gpt-4o-mini',
      messagesCount: messages?.length || 0,
      messages: messages?.map((msg, idx) => ({
        index: idx,
        role: msg.role,
        content: msg.content,
        contentLength: msg.content?.length || 0,
        toolInvocations: msg.toolInvocations?.length || 0
      })) || [],
      systemPrompt: `You are a helpful AI assistant with deep research capabilities.
When the user asks for research, use the deepResearch tool.
You may ask clarifying questions conversationally to improve research quality.
Always explain what you're doing.`,
      tools: ['deepResearch'],
      maxSteps: 5
    })
    
    // Log the system prompt for debugging
    // v5: System prompt passed to streamText - defines AI behavior
    const systemPrompt = getDeepResearchPrompt('TOOL_ORCHESTRATOR');
    
    console.log(`[${timestamp}] 📝 SYSTEM PROMPT [${requestId}]:`, {
      requestId,
      systemPromptLength: systemPrompt.length,
      systemPrompt: systemPrompt.substring(0, 200) + '...'
    });
    
    // v5: Validate messages before converting
    console.log(`[${timestamp}] 🔍 Pre-conversion message check:`, {
      hasMessages: !!messages,
      isArray: Array.isArray(messages),
      messageType: typeof messages,
      firstMessage: messages?.[0]
    })
    
    // v5: Convert messages safely
    let convertedMessages
    try {
      console.log(`[${timestamp}] 🔄 Converting messages:`, {
        messagesBeforeConvert: messages,
        messagesType: typeof messages,
        isArray: Array.isArray(messages),
        length: messages?.length
      })
      
      // v5: According to docs, streamText can accept UIMessages directly
      // Try converting to model messages first
      convertedMessages = convertToModelMessages(messages)
      
      console.log(`[${timestamp}] ✅ Messages converted successfully:`, {
        convertedLength: convertedMessages?.length,
        firstConverted: convertedMessages?.[0]
      })
    } catch (conversionError) {
      console.error(`[${timestamp}] ❌ Message conversion failed:`, {
        error: conversionError instanceof Error ? conversionError.message : conversionError,
        stack: conversionError instanceof Error ? conversionError.stack : undefined,
        messages: messages
      })
      
      // v5: Fallback - use messages directly as streamText can accept UIMessages
      convertedMessages = messages
    }
    
    // v5: streamText configuration - the core streaming function
    const result = streamText({
      model: openai('gpt-4o-mini'), // v5: model instance from provider
      messages: convertedMessages, // v5: Use converted messages or fallback
      tools: { 
        deepResearch: deepResearchTool,
        checkResearchStatus 
      }, // v5: tools object with tool definitions
      system: systemPrompt, // v5: system prompt for behavior
      maxSteps: 5, // v5: max tool execution steps
      // v5: onFinish callback - called when streaming completes
      onFinish: async ({ text, finishReason, usage, toolCalls, toolResults, response }) => {
        // v5: response metadata available in onFinish
        console.log(`[${timestamp}] 🎯 AI RESPONSE COMPLETE [${requestId}]:`, {
          requestId,
          finishReason,
          usage: {
            promptTokens: (usage as any).promptTokens || 0,
            completionTokens: (usage as any).completionTokens || 0,
            totalTokens: (usage as any).totalTokens || 0
          },
          responseText: text,
          responseLength: text.length,
          toolCallsCount: toolCalls.length,
          toolResultsCount: toolResults.length,
          toolCalls: toolCalls.map(tc => ({
            toolName: tc.toolName,
            toolCallId: tc.toolCallId,
            args: tc.args
          })),
          toolResults: toolResults.map(tr => ({
            toolName: tr.toolName,
            toolCallId: tr.toolCallId,
            result: tr.result
          })),
          responseId: response.id,
          responseModel: (response as any).modelId || (response as any).model,
          responseTimestamp: response.timestamp,
          messagesGenerated: response.messages.length
        })
        
        // v5: Save messages using custom appendResponseMessages implementation
        if (conversationId && response.messages) {
          try {
            console.log(`[${timestamp}] 💾 Starting message persistence for conversation [${conversationId}]`)
            
            // Import required functions
            const { ensureConversation } = await import('@/lib/api/chat/database/operations')
            const { prisma } = await import('@/lib/prisma')
            
            // First check if conversation exists
            const existingConversation = await prisma.conversation.findUnique({
              where: { id: conversationId }
            })
            
            console.log(`[${timestamp}] 🔍 Conversation exists? ${!!existingConversation}`, {
              conversationId,
              existingId: existingConversation?.id,
              existingTitle: existingConversation?.title
            })
            
            // Ensure conversation exists (create if needed)
            const finalConversationId = await ensureConversation(
              conversationId,
              undefined, // No user ID for test
              manualModel || 'openai/gpt-4o-mini',
              incomingMessages, // Use original incoming messages
              undefined // No anonymous session for now
            )
            
            console.log(`[${timestamp}] ✅ Conversation ensured with ID [${finalConversationId}]`)
            
            // Properly merge all messages using appendResponseMessages
            const allMessages = appendResponseMessages({
              messages: messages as UIMessage[],
              responseMessages: response.messages
            })
            
            console.log(`[${timestamp}] 📊 Merged message count: ${allMessages.length}`)
            
            // Save all messages to database
            // Delete existing messages first to avoid duplicates
            await prisma.message.deleteMany({
              where: { conversationId: finalConversationId }
            })
            
            // Save each message with proper metadata
            for (const msg of allMessages) {
              const textContent = msg.parts.find(p => p.type === 'text' && 'text' in p)?.text || ''
              
              await prisma.message.create({
                data: {
                  id: msg.id,
                  conversationId: finalConversationId,
                  role: msg.role,
                  content: textContent,
                  metadata: JSON.stringify({
                    parts: msg.parts || [],
                    annotations: (msg as any).annotations || [],
                    toolInvocations: (msg as any).toolInvocations || []
                  }),
                  createdAt: new Date()
                }
              })
            }
            
            console.log(`[${timestamp}] ✅ Saved ${allMessages.length} messages to database`)
            
            // Update conversation metadata
            await prisma.conversation.update({
              where: { id: finalConversationId },
              data: {
                lastMessageAt: new Date(),
                messageCount: allMessages.length,
                tokenCount: usage?.totalTokens || 0
              }
            })
            
            console.log(`[${timestamp}] 💾 Message persistence completed for conversation [${finalConversationId}]`)
          } catch (error) {
            console.error(`[${timestamp}] ❌ Failed to persist messages:`, error)
            if (error instanceof Error) {
              console.error(`[${timestamp}] Error details:`, {
                message: error.message,
                stack: error.stack
              })
            }
          }
        } else {
          console.log(`[${timestamp}] ⚠️ No conversation ID or response messages, skipping persistence`)
        }
      },
      onError({ error }) {
        console.error(`[${timestamp}] 💥 AI ERROR [${requestId}]:`, {
          requestId,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          type: error instanceof Error ? error.constructor.name : typeof error
        })
      }
    })
    
    console.log(`[${timestamp}] 📤 STREAMING RESPONSE [${requestId}]`)
    // v5: toUIMessageStreamResponse() converts to UI-compatible stream format
    return result.toUIMessageStreamResponse()
    
  } catch (error) {
    console.error(`[${timestamp}] 🚨 REQUEST ERROR [${requestId}]:`, {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      type: error instanceof Error ? error.constructor.name : typeof error
    })
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    return new Response(
      JSON.stringify({ error: 'Test failed', details: errorMessage, requestId }), 
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}