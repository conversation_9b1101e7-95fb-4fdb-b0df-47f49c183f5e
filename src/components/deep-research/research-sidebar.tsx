'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/sheet'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Button } from '@/components/ui/button'
import { 
  Brain, 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  ChevronDown,
  ChevronRight,
  Link,
  FileText,
  Activity,
  X
} from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import { cn } from '@/lib/utils'

interface Source {
  id: string
  title: string
  url: string
  relevance: number
  snippet: string
  timestamp: string
}

interface ResearchResult {
  id: string
  query: string
  summary: string
  analysis: string
  keyFindings: string[]
  sources: Source[]
  confidence: number
  timestamp: string
  metadata?: {
    version?: string
    worker?: string
    searchQueries?: number
  }
}

interface ResearchStatus {
  sessionId: string
  query: string
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  currentPhase: string
  metadata?: {
    executionTime?: number
    sourcesFound?: number
    searchQueriesCount?: number
  }
  result?: ResearchResult
  createdAt: string
  isLive: boolean
}

interface ResearchSidebarProps {
  sessionId: string | null
  isOpen: boolean
  onClose: () => void
}

interface LiveActivity {
  type: 'search' | 'crawl' | 'analyze'
  content: string
  timestamp: Date
}

export function ResearchSidebar({ sessionId, isOpen, onClose }: ResearchSidebarProps) {
  const [status, setStatus] = useState<ResearchStatus | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'progress' | 'results'>('progress')
  const [expandedSources, setExpandedSources] = useState<Set<string>>(new Set())
  const [liveActivities, setLiveActivities] = useState<LiveActivity[]>([])
  const [sourceCount, setSourceCount] = useState(0)

  // Poll for status updates
  useEffect(() => {
    if (!sessionId || !isOpen) return

    let pollCount = 0
    const maxPolls = 300 // 10 minutes max

    const fetchStatus = async () => {
      try {
        const response = await fetch(`/api/deep-research/status/${sessionId}`)
        if (!response.ok) {
          throw new Error('Failed to fetch status')
        }
        
        const data: ResearchStatus = await response.json()
        setStatus(data)
        
        // Extract live activities from current phase
        if (data.currentPhase) {
          const phase = data.currentPhase.toLowerCase()
          
          // Check for search queries
          if (phase.includes('searching for') || phase.includes('querying')) {
            const searchMatch = phase.match(/searching for[:\s]+["']?([^"']+)["']?/i)
            if (searchMatch) {
              setLiveActivities(prev => [...prev.slice(-4), {
                type: 'search',
                content: searchMatch[1],
                timestamp: new Date()
              }])
            }
          }
          
          // Check for crawling URLs
          if (phase.includes('analyzing') || phase.includes('crawling') || phase.includes('reading')) {
            const urlMatch = phase.match(/(?:analyzing|crawling|reading)[:\s]+(?:https?:\/\/)?([^\s]+)/i)
            if (urlMatch) {
              setLiveActivities(prev => [...prev.slice(-4), {
                type: 'crawl',
                content: urlMatch[1],
                timestamp: new Date()
              }])
            }
          }
          
          // Update source count
          const sourceMatch = phase.match(/(\d+)\s*(?:sources?|results?)/i)
          if (sourceMatch) {
            setSourceCount(parseInt(sourceMatch[1]))
          }
        }
        
        // Also check metadata for source count
        if (data.metadata?.sourcesFound) {
          setSourceCount(data.metadata.sourcesFound)
        }
        
        // Switch to results tab when complete
        if (data.status === 'completed' && data.result) {
          setActiveTab('results')
        }
        
        // Stop polling if complete or failed
        if (['completed', 'failed', 'cancelled'].includes(data.status)) {
          return true // Stop polling
        }
        
        return false // Continue polling
      } catch (err) {
        console.error('Failed to fetch research status:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch status')
        return false
      }
    }

    // Initial fetch
    fetchStatus()

    // Set up polling
    const pollInterval = setInterval(async () => {
      pollCount++
      if (pollCount > maxPolls) {
        clearInterval(pollInterval)
        setError('Research timeout - taking longer than expected')
        return
      }

      const shouldStop = await fetchStatus()
      if (shouldStop) {
        clearInterval(pollInterval)
      }
    }, 2000) // Poll every 2 seconds

    return () => clearInterval(pollInterval)
  }, [sessionId, isOpen])

  const toggleSource = (sourceId: string) => {
    setExpandedSources(prev => {
      const next = new Set(prev)
      if (next.has(sourceId)) {
        next.delete(sourceId)
      } else {
        next.add(sourceId)
      }
      return next
    })
  }

  const getStatusIcon = () => {
    if (!status) return null
    
    switch (status.status) {
      case 'pending':
        return <Activity className="w-5 h-5 text-muted-foreground" />
      case 'processing':
        return <Loader2 className="w-5 h-5 animate-spin text-primary" />
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'failed':
      case 'cancelled':
        return <AlertCircle className="w-5 h-5 text-red-600" />
    }
  }

  const getPhaseDescription = (phase: string) => {
    // Parse phase messages for better display
    if (phase.includes('[') && phase.includes('/')) {
      const match = phase.match(/^([\w-]+):\s*(.+?)\s*\[(\d+)\/(\d+)\]/)
      if (match) {
        const [, phaseName, description, current, total] = match
        return {
          name: phaseName.replace(/-/g, ' '),
          description,
          progress: `${current}/${total}`
        }
      }
    }
    
    if (phase.includes('Found') && phase.includes('sources')) {
      const match = phase.match(/Found (\d+) sources/)
      if (match) {
        return {
          name: 'Source Discovery',
          description: `Found ${match[1]} sources`,
          progress: match[1]
        }
      }
    }
    
    return {
      name: phase.split(':')[0] || phase,
      description: phase.split(':')[1]?.trim() || phase,
      progress: null
    }
  }

  if (!isOpen) return null

  return (
    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <SheetContent side="right" className="w-[600px] p-0">
        <SheetHeader className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Brain className="w-5 h-5" />
              <SheetTitle>Deep Research</SheetTitle>
              {sourceCount > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {sourceCount} sources
                </Badge>
              )}
              {getStatusIcon()}
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </SheetHeader>

        <Tabs 
          value={activeTab} 
          onValueChange={(v) => setActiveTab(v as 'progress' | 'results')}
          className="flex-1 flex flex-col h-full"
        >
          <TabsList className="mx-6 mt-4 grid w-[calc(100%-3rem)] grid-cols-2">
            <TabsTrigger value="progress">
              Progress
              {status?.status === 'processing' && (
                <Loader2 className="ml-2 h-3 w-3 animate-spin" />
              )}
            </TabsTrigger>
            <TabsTrigger value="results" disabled={!status?.result}>
              Results
              {status?.result && (
                <Badge variant="secondary" className="ml-2 h-5 px-1">
                  {status.metadata?.sourcesFound || 0}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <ScrollArea className="flex-1">
            <TabsContent value="progress" className="p-6 space-y-6">
              {status && (
                <>
                  {/* Query */}
                  <div>
                    <h3 className="text-sm font-medium mb-2">Research Query</h3>
                    <p className="text-sm text-muted-foreground">
                      {status.query}
                    </p>
                  </div>

                  {/* Progress Bar */}
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="font-medium">Overall Progress</span>
                      <span className="text-muted-foreground">{status.progress}%</span>
                    </div>
                    <Progress value={status.progress} className="h-2" />
                  </div>

                  {/* Current Phase */}
                  <div>
                    <h3 className="text-sm font-medium mb-2">Current Phase</h3>
                    <Card>
                      <CardContent className="p-4">
                        {(() => {
                          const phase = getPhaseDescription(status.currentPhase)
                          return (
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="font-medium capitalize">
                                  {phase.name}
                                </span>
                                {phase.progress && (
                                  <Badge variant="secondary">{phase.progress}</Badge>
                                )}
                              </div>
                              {phase.description !== phase.name && (
                                <p className="text-sm text-muted-foreground">
                                  {phase.description}
                                </p>
                              )}
                            </div>
                          )
                        })()}
                      </CardContent>
                    </Card>
                  </div>
                  
                  {/* Live Activity Feed */}
                  {(liveActivities.length > 0 || (status.status === 'processing' && status.currentPhase)) && (
                    <div>
                      <h3 className="text-sm font-medium mb-2">Live Activity</h3>
                      <Card>
                        <CardContent className="p-4 space-y-3">
                          {/* Current activity indicator */}
                          {status.status === 'processing' && (
                            <div className="flex items-start gap-2 pb-3 border-b">
                              <Activity className="w-4 h-4 mt-0.5 text-primary animate-pulse" />
                              <div className="space-y-1 flex-1">
                                <p className="text-sm font-medium">
                                  {status.currentPhase.includes('web-search') ? 'Searching the web...' :
                                   status.currentPhase.includes('analyze') ? 'Analyzing results...' :
                                   'Processing...'}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {status.currentPhase}
                                </p>
                              </div>
                            </div>
                          )}
                          
                          {/* Recent activities */}
                          <div className="space-y-2 max-h-[200px] overflow-y-auto">
                            {liveActivities.map((activity, idx) => (
                              <div key={idx} className="flex items-start gap-2 text-xs">
                                {activity.type === 'search' ? (
                                  <>
                                    <Activity className="w-3 h-3 mt-0.5 text-blue-500" />
                                    <div className="flex-1">
                                      <span className="font-medium">Searched:</span>
                                      <p className="text-muted-foreground">{activity.content}</p>
                                    </div>
                                  </>
                                ) : activity.type === 'crawl' ? (
                                  <>
                                    <Link className="w-3 h-3 mt-0.5 text-green-500" />
                                    <div className="flex-1">
                                      <span className="font-medium">Analyzing:</span>
                                      <p className="text-muted-foreground truncate">{activity.content}</p>
                                    </div>
                                  </>
                                ) : (
                                  <>
                                    <FileText className="w-3 h-3 mt-0.5 text-purple-500" />
                                    <div className="flex-1">
                                      <span className="font-medium">Processing:</span>
                                      <p className="text-muted-foreground">{activity.content}</p>
                                    </div>
                                  </>
                                )}
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  )}

                  {/* Metadata */}
                  {status.metadata && (
                    <div>
                      <h3 className="text-sm font-medium mb-2">Statistics</h3>
                      <div className="grid grid-cols-2 gap-4">
                        {status.metadata.searchQueriesCount !== undefined && (
                          <Card>
                            <CardContent className="p-4">
                              <div className="text-2xl font-bold">
                                {status.metadata.searchQueriesCount}
                              </div>
                              <p className="text-sm text-muted-foreground">
                                Search Queries
                              </p>
                            </CardContent>
                          </Card>
                        )}
                        {status.metadata.sourcesFound !== undefined && (
                          <Card>
                            <CardContent className="p-4">
                              <div className="text-2xl font-bold">
                                {status.metadata.sourcesFound}
                              </div>
                              <p className="text-sm text-muted-foreground">
                                Sources Found
                              </p>
                            </CardContent>
                          </Card>
                        )}
                        {status.metadata.executionTime !== undefined && (
                          <Card>
                            <CardContent className="p-4">
                              <div className="text-2xl font-bold">
                                {Math.round(status.metadata.executionTime / 1000)}s
                              </div>
                              <p className="text-sm text-muted-foreground">
                                Processing Time
                              </p>
                            </CardContent>
                          </Card>
                        )}
                      </div>
                    </div>
                  )}
                </>
              )}

              {error && (
                <Card className="border-red-200 dark:border-red-800">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                      <AlertCircle className="w-4 h-4" />
                      <span className="font-medium">Error</span>
                    </div>
                    <p className="text-sm mt-2">{error}</p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="results" className="p-6 space-y-6">
              {status?.result && (
                <>
                  {/* Summary */}
                  <div>
                    <h3 className="text-sm font-medium mb-2">Summary</h3>
                    <Card>
                      <CardContent className="p-4 prose prose-sm dark:prose-invert max-w-none">
                        <ReactMarkdown>{status.result.summary}</ReactMarkdown>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Key Findings */}
                  {status.result.keyFindings.length > 0 && (
                    <div>
                      <h3 className="text-sm font-medium mb-2">Key Findings</h3>
                      <Card>
                        <CardContent className="p-4">
                          <ul className="space-y-2">
                            {status.result.keyFindings.map((finding, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 shrink-0" />
                                <span className="text-sm">{finding}</span>
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>
                    </div>
                  )}

                  {/* Sources */}
                  <div>
                    <h3 className="text-sm font-medium mb-2">
                      Sources ({status.result.sources.length})
                    </h3>
                    <div className="space-y-2">
                      {status.result.sources.map((source) => (
                        <Collapsible
                          key={source.id}
                          open={expandedSources.has(source.id)}
                          onOpenChange={() => toggleSource(source.id)}
                        >
                          <Card>
                            <CollapsibleTrigger className="w-full">
                              <CardContent className="p-4">
                                <div className="flex items-start gap-2">
                                  <div className="mt-1">
                                    {expandedSources.has(source.id) ? (
                                      <ChevronDown className="w-4 h-4" />
                                    ) : (
                                      <ChevronRight className="w-4 h-4" />
                                    )}
                                  </div>
                                  <div className="flex-1 text-left">
                                    <div className="flex items-start justify-between gap-2">
                                      <h4 className="text-sm font-medium line-clamp-2">
                                        {source.title}
                                      </h4>
                                      <Badge variant="secondary" className="shrink-0">
                                        {Math.round(source.relevance * 100)}%
                                      </Badge>
                                    </div>
                                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                      {source.snippet}
                                    </p>
                                  </div>
                                </div>
                              </CardContent>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                              <CardContent className="pt-0 pb-4 px-4">
                                <div className="pl-6 space-y-2">
                                  <a
                                    href={source.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="inline-flex items-center gap-1 text-sm text-primary hover:underline"
                                  >
                                    <Link className="w-3 h-3" />
                                    View Source
                                  </a>
                                  <p className="text-sm text-muted-foreground">
                                    {source.snippet}
                                  </p>
                                </div>
                              </CardContent>
                            </CollapsibleContent>
                          </Card>
                        </Collapsible>
                      ))}
                    </div>
                  </div>

                  {/* Full Analysis (expandable) */}
                  <Collapsible>
                    <CollapsibleTrigger className="flex items-center gap-2 text-sm font-medium">
                      <FileText className="w-4 h-4" />
                      View Full Analysis
                      <ChevronDown className="w-4 h-4" />
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <Card className="mt-2">
                        <CardContent className="p-4 prose prose-sm dark:prose-invert max-w-none">
                          <ReactMarkdown>{status.result.analysis}</ReactMarkdown>
                        </CardContent>
                      </Card>
                    </CollapsibleContent>
                  </Collapsible>

                  {/* Confidence Score */}
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Confidence Score</span>
                        <div className="flex items-center gap-2">
                          <Progress 
                            value={status.result.confidence * 100} 
                            className="w-24 h-2"
                          />
                          <span className="text-sm">
                            {Math.round(status.result.confidence * 100)}%
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </TabsContent>
          </ScrollArea>
        </Tabs>
      </SheetContent>
    </Sheet>
  )
}